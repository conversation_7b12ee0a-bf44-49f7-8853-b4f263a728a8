{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue", "mtime": 1753698968271}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sankeyChart2.vue"], "names": [], "mappings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file": "sankeyChart2.vue", "sourceRoot": "src/views/bigScreenSanhao/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"bg-box\">\r\n      <div class=\"bg-box-title\">\r\n        <span>{{ title }}</span>\r\n        <div class=\"date-picker-container\">\r\n          <!-- 日期区间选择器 -->\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            @change=\"onDateRangeChange\"\r\n            size=\"small\"\r\n            class=\"title-date-picker\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bg-box-content\">\r\n        <div\r\n          class=\"sankey-container\"\r\n          :style=\"{ width: width, height: hasData ? containerHeight : '100px' }\"\r\n        >\r\n          <div\r\n            ref=\"sankeyChart\"\r\n            class=\"sankey-chart\"\r\n            :style=\"{ height: dynamicHeight }\"\r\n            v-show=\"hasData\"\r\n          ></div>\r\n          <div v-show=\"!hasData\" class=\"no-data-container\">\r\n            <div class=\"no-data-text\">暂无数据</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { zcfxSankey } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"SankeyChart2\",\r\n  props: {\r\n    width: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"关系图\",\r\n    },\r\n    // 接口参数类型：proposalsTitle、proposalsExperts、enterpriseName\r\n    paramType: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    // 参数值\r\n    paramValue: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null,\r\n      loading: false,\r\n      sankeyData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 原始数据，包含所有节点和连接\r\n      originalData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 记录哪些子标签节点已展开企业\r\n      expandedSubLabels: new Set(),\r\n      // ResizeObserver 实例\r\n      resizeObserver: null,\r\n      // 防抖定时器\r\n      resizeTimer: null,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 是否有数据\r\n      hasData: true,\r\n      // 基础高度\r\n      baseHeight: 600,\r\n      // 每个展开企业增加的高度\r\n      heightPerExpansion: 50,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算桑葚图高度\r\n    dynamicHeight() {\r\n      // 计算当前显示的企业节点数量\r\n      const enterpriseCount = this.sankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n\r\n      // 基础高度 + 企业数量 * 每个企业增加的高度\r\n      const calculatedHeight =\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion;\r\n      return `${calculatedHeight}px`;\r\n    },\r\n\r\n    // 动态计算容器高度（用于滚动）\r\n    containerHeight() {\r\n      // 容器高度固定为基础高度，超出部分显示滚动条\r\n      return `${this.baseHeight}px`;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.initDefaultDateRange();\r\n    this.initChart();\r\n    this.fetchSankeyData();\r\n  },\r\n\r\n  watch: {\r\n    // 监听宽度和高度变化\r\n    width() {\r\n      this.handleResize();\r\n    },\r\n    height() {\r\n      this.handleResize();\r\n    },\r\n    // 监听参数变化\r\n    paramValue() {\r\n      this.fetchSankeyData();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n    // 清理 ResizeObserver\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n    }\r\n    // 清理定时器\r\n    if (this.resizeTimer) {\r\n      clearTimeout(this.resizeTimer);\r\n    }\r\n    // 清理窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleResize);\r\n  },\r\n  methods: {\r\n    // 初始化默认日期范围（最近半年）\r\n    initDefaultDateRange() {\r\n      const today = new Date();\r\n      const sixMonthsAgo = new Date();\r\n      sixMonthsAgo.setMonth(today.getMonth() - 6);\r\n\r\n      this.dateRange = [this.formatDate(sixMonthsAgo), this.formatDate(today)];\r\n    },\r\n\r\n    // 格式化日期为 yyyy-MM-dd 格式\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n      const day = String(date.getDate()).padStart(2, \"0\");\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n    // 日期范围变化处理\r\n    onDateRangeChange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 更新日期范围（由父组件调用）\r\n    updateDateRange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 获取桑葚图数据\r\n    async fetchSankeyData() {\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 构建请求参数\r\n        const params = {\r\n          screenSn: \"1\",\r\n        };\r\n\r\n        // 根据参数类型添加对应的参数\r\n        if (this.paramType && this.paramValue) {\r\n          params[this.paramType] = this.paramValue;\r\n        }\r\n\r\n        // 如果有选择日期范围，添加日期参数\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.startDate = this.dateRange[0];\r\n          params.endDate = this.dateRange[1];\r\n        }\r\n\r\n        const response = await zcfxSankey(params);\r\n\r\n        if (response && response.data) {\r\n          console.log(\"桑葚图数据:\", response.data);\r\n          // 处理节点数据\r\n          const processedNodes = this.processNodes(response.data.nodes || []);\r\n          // 处理连接数据\r\n          const processedLinks = this.processLinks(response.data.links || []);\r\n\r\n          // 保存原始数据\r\n          this.originalData = {\r\n            nodes: processedNodes,\r\n            links: processedLinks,\r\n          };\r\n\r\n          // 初始化时隐藏企业节点\r\n          this.sankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n          if (this.sankeyData.links.length === 0) {\r\n            this.hasData = false;\r\n            this.sankeyData.nodes = [];\r\n          } else {\r\n            this.hasData = true;\r\n            // 如果有数据但图表还没初始化，则初始化图表\r\n            if (!this.chart) {\r\n              this.$nextTick(() => {\r\n                this.initChart();\r\n              });\r\n            }\r\n          }\r\n\r\n          // 更新图表\r\n          this.updateChart();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取桑葚图数据失败:\", error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理节点数据\r\n    processNodes(nodes) {\r\n      const colors = [\"#dd79ff\", \"#58d9f9\", \"#4992ff\"];\r\n\r\n      // category到层级的映射\r\n      const categoryToDepth = {\r\n        党派: 0,\r\n        专家: 1,\r\n        提案: 2,\r\n        父标签: 3,\r\n        子标签: 4,\r\n        企业: 5,\r\n      };\r\n\r\n      return nodes.map((node, index) => {\r\n        const depth =\r\n          categoryToDepth[node.category] !== undefined\r\n            ? categoryToDepth[node.category]\r\n            : 0;\r\n\r\n        return {\r\n          id: node.id,\r\n          name: node.name,\r\n          category: node.category,\r\n          depth: depth,\r\n          itemStyle: {\r\n            color: colors[index % colors.length],\r\n          },\r\n        };\r\n      });\r\n    },\r\n\r\n    // 过滤企业节点，根据展开状态决定是否显示\r\n    filterEnterpriseNodes(data) {\r\n      // 如果没有展开任何子标签，则隐藏所有企业节点\r\n      if (this.expandedSubLabels.size === 0) {\r\n        const filteredNodes = data.nodes.filter(\r\n          (node) => node.category !== \"企业\"\r\n        );\r\n        const filteredLinks = data.links.filter((link) => {\r\n          const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n          const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n          return sourceExists && targetExists;\r\n        });\r\n\r\n        return {\r\n          nodes: filteredNodes,\r\n          links: filteredLinks,\r\n        };\r\n      }\r\n\r\n      // 如果有展开的子标签，则显示对应的企业节点\r\n      const filteredNodes = data.nodes.filter((node) => {\r\n        if (node.category === \"企业\") {\r\n          // 查找连接到此企业的子标签节点\r\n          const connectedToSubLabel = data.links.some((link) => {\r\n            // 检查企业是否与已展开的子标签相连\r\n            if (link.target === node.id) {\r\n              // 企业是目标，检查源是否是已展开的子标签\r\n              const sourceNode = data.nodes.find((n) => n.id === link.source);\r\n              return (\r\n                sourceNode &&\r\n                sourceNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(sourceNode.id)\r\n              );\r\n            }\r\n            if (link.source === node.id) {\r\n              // 企业是源，检查目标是否是已展开的子标签\r\n              const targetNode = data.nodes.find((n) => n.id === link.target);\r\n              return (\r\n                targetNode &&\r\n                targetNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(targetNode.id)\r\n              );\r\n            }\r\n            return false;\r\n          });\r\n\r\n          return connectedToSubLabel;\r\n        }\r\n        return true; // 非企业节点都显示\r\n      });\r\n\r\n      const filteredLinks = data.links.filter((link) => {\r\n        const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n        const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n        return sourceExists && targetExists;\r\n      });\r\n\r\n      return {\r\n        nodes: filteredNodes,\r\n        links: filteredLinks,\r\n      };\r\n    },\r\n\r\n    // 处理连接数据\r\n    processLinks(links) {\r\n      return links.map((link) => {\r\n        return {\r\n          source: link.source,\r\n          target: link.target,\r\n          value: link.value || 1, // 如果没有value，默认为1\r\n        };\r\n      });\r\n    },\r\n\r\n    // 更新图表\r\n    updateChart() {\r\n      if (this.chart && this.hasData) {\r\n        const option = this.getChartOption();\r\n        // 使用 notMerge: false 来避免完全重新渲染，提高性能\r\n        this.chart.setOption(option, true);\r\n      }\r\n    },\r\n\r\n    // 获取图表配置\r\n    getChartOption() {\r\n      return {\r\n        backgroundColor: \"transparent\",\r\n        title: {\r\n          text: \"\",\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n            fontSize: 16,\r\n          },\r\n        },\r\n        tooltip: {\r\n          trigger: \"item\",\r\n          triggerOn: \"mousemove\",\r\n          backgroundColor: \"rgba(0, 0, 0, 0.8)\",\r\n          borderColor: \"#0ec2f4\",\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n          },\r\n          formatter: function (params) {\r\n            if (params.dataType === \"edge\") {\r\n              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;\r\n            } else {\r\n              const depthMap = {\r\n                0: \"第1级\",\r\n                1: \"第2级\",\r\n                2: \"第3级\",\r\n                3: \"第4级\",\r\n                4: \"第5级\",\r\n                5: \"第6级\",\r\n              };\r\n              const levelText = depthMap[params.data.depth] || \"未知层级\";\r\n              return `${params.data.name}<br/>类别: ${\r\n                params.data.category || \"未知\"\r\n              }<br/>层级: ${levelText}`;\r\n            }\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            type: \"sankey\",\r\n            layout: \"none\",\r\n            emphasis: {\r\n              focus: \"adjacency\",\r\n            },\r\n            data: this.sankeyData.nodes,\r\n            links: this.sankeyData.links,\r\n            // orient: 'vertical',\r\n            // nodeAlign: 'justify',\r\n            nodeGap: 10,\r\n            nodeWidth: 40,\r\n            layoutIterations: 0,\r\n            left: \"0\",\r\n            right: \"20%\",\r\n            top: \"0.2%\",\r\n            bottom: \"0.2%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#ffffff\",\r\n              fontSize: 14,\r\n              formatter: function (params) {\r\n                return params.name.length > 12\r\n                  ? params.name.substring(0, 12) + \"...\"\r\n                  : params.name;\r\n              },\r\n            },\r\n            lineStyle: {\r\n              color: \"source\",\r\n              // curveness: 0.2,\r\n              // opacity: 0.7\r\n            },\r\n            // itemStyle: {\r\n            //   borderWidth: 1,\r\n            //   borderColor: '#0ec2f4'\r\n            // }\r\n          },\r\n        ],\r\n      };\r\n    },\r\n\r\n    initChart() {\r\n      if (!this.hasData) {\r\n        return; // 没有数据时不初始化图表\r\n      }\r\n\r\n      // 设置图表容器的尺寸 - 初始化时使用基础高度\r\n      this.$refs.sankeyChart.style.width = \"100%\";\r\n      this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n\r\n      this.chart = echarts.init(this.$refs.sankeyChart);\r\n\r\n      // 初始化时设置空的图表配置\r\n      const option = this.getChartOption();\r\n      this.chart.setOption(option);\r\n\r\n      // 添加节点点击事件\r\n      this.chart.on(\"click\", (params) => {\r\n        if (params.dataType === \"node\" && params.data.category === \"子标签\") {\r\n          this.toggleEnterpriseNodes(params.data.id);\r\n        }\r\n      });\r\n\r\n      // 设置尺寸变化监听\r\n      this.setupResizeListeners();\r\n    },\r\n\r\n    // 切换企业节点的显示/隐藏\r\n    toggleEnterpriseNodes(subLabelId) {\r\n      if (this.expandedSubLabels.has(subLabelId)) {\r\n        // 如果已展开，则收起\r\n        this.expandedSubLabels.delete(subLabelId);\r\n      } else {\r\n        // 如果未展开，则展开\r\n        this.expandedSubLabels.add(subLabelId);\r\n      }\r\n\r\n      // 重新过滤数据\r\n      const newSankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n      // 计算新高度\r\n      const enterpriseCount = newSankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n      const newHeight = `${\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion\r\n      }px`;\r\n\r\n      // 同时更新数据、高度和图表，确保完全同步\r\n      this.sankeyData = newSankeyData;\r\n      if (this.$refs.sankeyChart) {\r\n        this.$refs.sankeyChart.style.height = newHeight;\r\n      }\r\n\r\n      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度\r\n      if (this.expandedSubLabels.size === 0) {\r\n        // 先强制设置高度为基础高度\r\n        if (this.$refs.sankeyChart) {\r\n          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n          // 强制浏览器重新计算布局\r\n          this.$refs.sankeyChart.offsetHeight;\r\n        }\r\n\r\n        // 立即更新图表以确保高度生效\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n\r\n        // 然后滚动到顶部\r\n        const container = this.$refs.sankeyChart.parentElement;\r\n        if (container) {\r\n          // 强制触发重绘\r\n          container.offsetHeight;\r\n          container.scrollTop = 0;\r\n          // 使用 scrollTo 确保滚动生效\r\n          container.scrollTo({ top: 0, behavior: \"instant\" });\r\n        }\r\n      }\r\n\r\n      // 立即更新图表\r\n      this.updateChart();\r\n      if (this.chart) {\r\n        this.chart.resize();\r\n      }\r\n    },\r\n\r\n    // 设置尺寸变化监听\r\n    setupResizeListeners() {\r\n      // 创建 ResizeObserver 监听容器尺寸变化\r\n      if (window.ResizeObserver) {\r\n        this.resizeObserver = new ResizeObserver(() => {\r\n          // 使用防抖处理，避免频繁触发\r\n          clearTimeout(this.resizeTimer);\r\n          this.resizeTimer = setTimeout(() => {\r\n            this.handleResize();\r\n          }, 100);\r\n        });\r\n\r\n        // 监听图表容器的尺寸变化\r\n        this.resizeObserver.observe(this.$refs.sankeyChart);\r\n\r\n        // 也监听父容器的尺寸变化\r\n        const parentContainer = this.$refs.sankeyChart.parentElement;\r\n        if (parentContainer) {\r\n          this.resizeObserver.observe(parentContainer);\r\n        }\r\n      }\r\n\r\n      // 监听窗口大小变化（作为备用方案）\r\n      this.handleResize = this.handleResize.bind(this);\r\n      window.addEventListener(\"resize\", this.handleResize);\r\n    },\r\n\r\n    // 处理尺寸变化\r\n    handleResize() {\r\n      if (this.chart) {\r\n        // 延迟执行 resize，确保 DOM 更新完成\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    // 手动触发图表重新调整大小（供父组件调用）\r\n    resizeChart() {\r\n      this.handleResize();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bg-box {\r\n  position: relative;\r\n  background: #1b283b;\r\n  border-radius: 8px;\r\n  padding: 8px 16px 16px;\r\n  margin-bottom: 20px;\r\n\r\n  .bg-box-title {\r\n    font-weight: 800;\r\n    font-size: 18px;\r\n    color: #ffffff;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .bg-box-content {\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    white-space: pre-wrap;\r\n  }\r\n}\r\n\r\n.date-picker-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.title-date-picker {\r\n  width: 240px;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner {\r\n  background: rgba(27, 40, 59, 0.8) !important;\r\n  border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  color: #ffffff !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-separator {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input {\r\n  background: transparent !important;\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n// 额外的强制样式覆盖\r\n::v-deep .el-date-editor.el-input {\r\n  .el-input__wrapper {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  }\r\n\r\n  .el-input__inner {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n    color: #ffffff !important;\r\n  }\r\n}\r\n\r\n.sankey-container {\r\n  overflow-y: auto; /* 允许垂直滚动 */\r\n  overflow-x: hidden;\r\n  position: relative;\r\n}\r\n\r\n.sankey-chart {\r\n  width: 100%;\r\n  min-height: 600px; /* 设置最小高度，确保图表有足够空间渲染 */\r\n  /* 完全移除过渡动画，实现瞬间变化 */\r\n}\r\n\r\n.no-data-container {\r\n  width: 100%;\r\n  height: 100px; /* 无数据时的较小高度 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.no-data-text {\r\n  color: #fff;\r\n  font-size: 16px;\r\n  text-align: center;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.sankey-container::-webkit-scrollbar,\r\n.sankey-fullscreen-container::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-track,\r\n.sankey-fullscreen-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb:hover,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n</style>\r\n"]}]}