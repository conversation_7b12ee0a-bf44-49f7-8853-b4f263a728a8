<template>
  <div style="height: 100%; display: flex" class="two">
    <div class="left">
      <div class="bsContentBox" style="width: 516px; height: 380px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">热点推荐</div>
          <!-- <div class="bsContentTitleHelp"></div> -->

          <div
            class="bsContentTitleMore"
            @click="openNewTab('MonitorUse?id=1')"
            style="right: 80px"
          >
            更多
          </div>
          <div class="bsContentTitleMore" @click="openSina">舆情通</div>
        </div>
        <div class="bsContentContent">
          <div class="remengwenzhang-box" :style="remengwenzhangBoxStyle">
            <div
              class="scroll-wrapper"
              ref="scrollWrapper"
              @mouseenter="handleMouseEnter"
              @mouseleave="handleMouseLeave"
              @scroll="updateScrollbar"
            >
              <div class="scroll-content" ref="scrollContent">
                <div
                  class="remengwenzhang-list"
                  v-for="(item, index) in remengwenzhangList"
                  :key="index"
                  @click="openNewView(item)"
                >
                  <div
                    class="block"
                    :style="{
                      background: item.isShow === '3' ? '#F48200' : '#1bdcff',
                    }"
                  ></div>
                  <div class="title">{{ item.title }}</div>
                  <div class="sourceName">{{ item.sourceName }}</div>
                  <div class="time">
                    {{ parseTime(item.publishTime, "{y}-{m}-{d}") }}
                  </div>
                </div>
              </div>
            </div>
            <div class="scroll-bar"></div>
          </div>

          <!-- 文章通知组件 -->
          <article-notification
            ref="articleNotification"
            :articles="notificationArticles"
            :visible="showNotification"
            @close="handleNotificationClose"
            @view-article="handleViewArticle"
          />
        </div>
      </div>
      <div class="bsContentBox" style="width: 516px; height: 290px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">政策风险</div>
          <div class="bsContentTitleHelp" @click="getPolicyRiskDetail"></div>
          <div
            class="bsContentTitleMore"
            @click="openNewTab('http://61.149.6.16:21001/bigScreen')"
          >
            更多
          </div>
        </div>
        <div class="bsContentContent">
          <usaMap
            style="width: 516px; height: 247px"
            :external-data="usaMapData"
          ></usaMap>
        </div>
      </div>
      <div class="bsContentBox" style="width: 516px; height: 290px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">打压风险</div>
          <div class="bsContentTitleHelp" @click="getRiskDetail"></div>
          <div
            class="bsContentTitleMore"
            @click="openNewTab('http://61.149.6.16:21001/bigScreen')"
          >
            更多
          </div>
        </div>
        <div class="bsContentContent">
          <timeLine
            :timelineEvents="suppressListData"
            @openEnterpriseInformation="openEnterpriseInformation"
            style="width: 516px; height: 247px"
          ></timeLine>
        </div>
      </div>
    </div>
    <div class="center">
      <div class="center-top">
        <div class="top-content">
          <div class="bg1"></div>
          <div class="top-content-number">
            {{ padWithZeros(gatherTotal, 6) }}
          </div>
          <div class="top-content-name">有效采集量</div>
        </div>
        <div class="top-content">
          <div class="bg1"></div>
          <div class="top-content-number">
            {{ padWithZeros(gatherDayNumber, 6) }}
          </div>
          <div class="top-content-name">当日采集数量</div>
        </div>
      </div>
      <div
        class="bsContentBox1"
        @mouseenter="handleMouseEnter2"
        @mouseleave="handleMouseLeave2"
      >
        <div class="bsContentTitle1">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName" style="display: flex">
            <div
              @click="zhikuActive = 0"
              :style="{ fontWeight: zhikuActive === 0 ? '800' : '400' }"
              style="cursor: pointer"
            >
              重点人物分析
            </div>
            <div style="margin: 0 4px">/</div>
            <div
              @click="zhikuActive = 1"
              :style="{ fontWeight: zhikuActive === 1 ? '800' : '400' }"
              style="cursor: pointer"
            >
              产业与技术专题分析
            </div>
          </div>
          <!-- <div class="bsContentTitleHelp" ></div> -->
        </div>
        <div
          class="bsContentContent"
          style="display: flex; flex-direction: column; gap: 8px; padding: 8px"
        >
          <!-- Tab 切换按钮 -->
          <div class="tab-buttons" v-if="zhikuActive === 0">
            <div
              class="tab-button"
              :class="{ active: activeTab === 'trump' }"
              @click="switchTab('trump')"
            >
              特朗普
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'msk' }"
              @click="switchTab('msk')"
            >
              埃隆·里夫·马斯克
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'ws' }"
              @click="switchTab('ws')"
            >
              詹姆斯·唐纳德·万斯
            </div>
          </div>
          <div class="tab-buttons" v-if="zhikuActive === 1">
            <div
              class="tab-button"
              :class="{ active: activeTab === 'bdt' }"
              @click="switchTab('bdt', 7)"
            >
              半导体领域
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'gdzb' }"
              @click="switchTab('gdzb', 8)"
            >
              高端装备与材料
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'xnyqc' }"
              @click="switchTab('xnyqc', 9)"
            >
              新能源汽车与电池
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'szhzx' }"
              @click="switchTab('szhzx', 10)"
            >
              数字化转型与工业软件
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'lszz' }"
              @click="switchTab('lszz', 11)"
            >
              绿色制造与新能源
            </div>
            <div
              class="tab-button"
              :class="{ active: activeTab === 'swyy' }"
              @click="switchTab('swyy', 12)"
            >
              生物医药与医疗器械
            </div>
          </div>

          <!-- 人物观点内容（特朗普/马斯克公用） -->
          <div
            v-show="
              activeTab === 'trump' || activeTab === 'msk' || activeTab === 'ws'
            "
            class="tab-content"
          >
            <!-- 上方人物观点树状图 -->
            <div class="trump-view-container">
              <trumpViewTree
                ref="characterViewTree"
                @handleNodeClick="handleNodeClick"
                style="width: 100%; height: 300px"
                :move="isHovered2"
                :currentCharacter="activeTab"
              >
              </trumpViewTree>
            </div>
            <div class="view-tree-container">
              <viewTree
                :treeData="characterViewData"
                :title="'renwu'"
                :visible="
                  activeTab === 'trump' ||
                  activeTab === 'msk' ||
                  activeTab === 'ws'
                "
                style="width: 100%; height: 100%"
                @openNewView="openNewView1"
                @openbaarTreeEcharts="openbaarTreeEcharts()"
              ></viewTree>
            </div>
          </div>

          <!-- 中国制造短板分析内容 -->
          <div
            v-show="
              activeTab !== 'trump' && activeTab !== 'msk' && activeTab !== 'ws'
            "
            class="tab-content"
          >
            <svg ref="markmap" class="markmap-svg"></svg>
          </div>

          <div
            style=""
            class="bsContentTitleMore"
            @click="openbaarTreeEcharts()"
          >
            更多
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="bsContentBox" style="width: 516px; height: 380px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">前沿科技动态</div>
          <div class="bsContentTitleMore" @click="qykjdtOpenNewTab()">更多</div>
        </div>
        <div
          class="bsContentContent"
          @mouseleave="handleMouseLeave1"
          @scroll="updateScrollbar1"
        >
          <div class="kejidongtai-box">
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '脑机接口' }"
              @click="handleButtonClick('脑机接口')"
            >
              脑机接口
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '量子信息' }"
              @click="handleButtonClick('量子信息')"
            >
              量子信息
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '人形机器人' }"
              @click="handleButtonClick('人形机器人')"
            >
              人形机器人
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '生成式人工智能' }"
              @click="handleButtonClick('生成式人工智能')"
            >
              生成式人工智能
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '生物制造' }"
              @click="handleButtonClick('生物制造')"
            >
              生物制造
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '未来显示' }"
              @click="handleButtonClick('未来显示')"
            >
              未来显示
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '未来网络' }"
              @click="handleButtonClick('未来网络')"
            >
              未来网络
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '新型储能' }"
              @click="handleButtonClick('新型储能')"
            >
              新型储能
            </div>
            <div
              class="kejidongtai-button"
              :class="{ active: activeButton === '其他' }"
              @click="handleButtonClick('其他')"
            >
              其他
            </div>
          </div>
          <div class="remengwenzhang-box1">
            <div
              class="scroll-wrapper"
              ref="scrollWrapper1"
              @mouseenter="handleMouseEnter1"
            >
              <div class="scroll-content" ref="scrollContent1">
                <div
                  class="remengwenzhang-list"
                  v-for="(item, index) in remengwenzhangList1"
                  :key="index"
                  @click="openNewView1(item)"
                >
                  <div class="block"></div>
                  <div class="title">{{ item.cnTitle }}</div>
                  <div class="sourceName">{{ item.sourceName }}</div>
                  <div class="time">
                    {{ parseTime(item.publishTime, "{y}-{m}-{d}") }}
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="scroll-bar"></div> -->
          </div>
        </div>
      </div>
      <div class="bsContentBox2" style="width: 516px; height: 600px">
        <div class="bsContentTitle">
          <div class="bsContentTitleIcon"></div>
          <div class="bsContentTitleName">国内外前沿热点技术</div>
          <div
            class="bsContentTitleHelp"
            @click="comparisonChartShowModal = true"
          ></div>
          <div
            class="bsContentTitleMore"
            @click="
              openNewTab(
                'http://36.110.223.95:8080/analysis/#/infoQuery/queryManage'
              )
            "
          >
            更多
          </div>
        </div>
        <div class="kejidongtai-box">
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '11' }"
            @click="handleTechButtonClick('11', '新能源')"
          >
            新能源
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '12' }"
            @click="handleTechButtonClick('12', '新材料')"
          >
            新材料
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '13' }"
            @click="handleTechButtonClick('13', '高端装备')"
          >
            高端装备
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '14' }"
            @click="handleTechButtonClick('14', '新能源汽车')"
          >
            新能源汽车
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '17' }"
            @click="handleTechButtonClick('17', '船舶与海洋工程装备')"
          >
            船舶与海洋工程装备
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '16' }"
            @click="handleTechButtonClick('16', '民用航空')"
          >
            民用航空
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '15' }"
            @click="handleTechButtonClick('15', '绿色环保')"
          >
            绿色环保
          </div>
          <div
            class="kejidongtai-button"
            :class="{ active: activeTechButton === '18' }"
            @click="handleTechButtonClick('18', '新一代信息技术')"
          >
            新一代信息技术
          </div>
        </div>
        <!-- <div class="bsContentContent" style="height: 188px">
          <technologyArticles
            :sccenId="1"
            :screenSn="currentTechScreenSn"
            @openHotTechnology="openHotTechnology"
          ></technologyArticles>
        </div> -->
        <div class="bsContentContent" style="padding-top: 0px; height: 450px">
          <graphEcharts
            :sccenId="1"
            :screenSn="currentTechScreenSn"
            @openTechnologyDetails="openTechnologyDetails"
          ></graphEcharts>
        </div>
      </div>
    </div>

    <policyRisk
      :visible="policyRiskShowModal"
      :list1="policyRiskList1"
      :list2="policyRiskList2"
      :total="policyRiskList1Total"
      :usa-map-data="usaMapData"
      title="美国相关提案"
      @update:visible="policyRiskShowModal = $event"
      @pagination="policyRiskPagination"
      @openArticleDetail="openArticleDetails('policyRisk-news', $event)"
    >
    </policyRisk>
    <suppressionOfRisks
      :visible="suppressionOfRisksShowModal"
      :levelCount="riskBarChartData"
      :enterpriseList="riskEnterpriseList"
      :total="riskEnterpriseListTotal"
      title="打压风险"
      @update:visible="suppressionOfRisksShowModal = $event"
      @openEnterpriseInformation="openEnterpriseInformation"
      @pagination="riskEnterpriseListPagination"
    >
    </suppressionOfRisks>
    <technologyDetails
      :visible="technologyDetailsShowModal"
      @update:visible="technologyDetailsShowModal = $event"
      @openArticleDetail="(e) => openArticleDetails('technology-article', e)"
      :title="technologyDetailsTitle"
      :item="technologyDetailsItem"
    ></technologyDetails>
    <articleDetails
      :visible="articleDetailsShowModal"
      :title="articleDetailsTitle"
      :content="articleDetailsContent"
      :contentEn="articleDetailsContentEn"
      :item="articleDetailsItem"
      @update:visible="articleDetailsShowModal = $event"
    >
    </articleDetails>
    <enterpriseInformation
      :visible="enterpriseInformationShowModal"
      :title="enterpriseInformationTitle"
      :content="enterpriseInformationContent"
      :patentList="patentList"
      :softwareList="softwareList"
      :total1="patentTotal"
      :total2="softwareTotal"
      @update:visible="enterpriseInformationShowModal = $event"
      @pagination1="patentPagination"
      @pagination2="softwarePagination"
      @openArticleDetail="
        (e) => openArticleDetails('enterpriseInformation-news', e)
      "
    >
    </enterpriseInformation>
    <comparisonChart
      :visible="comparisonChartShowModal"
      @update:visible="comparisonChartShowModal = $event"
      @openHotTechnology="openHotTechnology"
      title="前沿技术热点对比图详情"
    ></comparisonChart>
    <hotTechnology
      :visible="hotTechnologyShowModal"
      :title="hotTechnologytTitle"
      :id="hotTechnologytID"
      @update:visible="hotTechnologyShowModal = $event"
    ></hotTechnology>
    <baarTreeEcharts
      :visible="baarTreeEchartsShowModal"
      :type="baarTreeEchartsType"
      title="智库观点"
      @update:visible="baarTreeEchartsShowModal = $event"
      @openNewView="openNewView1"
    ></baarTreeEcharts>

    <!-- 热点推荐文章详情弹窗 -->
    <el-dialog
      :title="drawerInfo.cnTitle || drawerInfo.title"
      :visible.sync="articleDialogVisible"
      width="65%"
      append-to-body
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="fz">
        <div class="text">字号：</div>
        <div class="btns">
          <div class="btn-minus" @click="decreaseFontSize">-</div>
          <div class="font-size">{{ fontSize }}px</div>
          <div class="btn-plus" @click="increaseFontSize">+</div>
        </div>
      </div>
      <div
        class="dialog-art"
        :style="{ fontSize: fontSize + 'px' }"
        v-html="drawerInfo.cnContent"
      ></div>
      <el-empty
        description="当前文章暂无数据"
        v-if="!drawerInfo.cnContent"
      ></el-empty>
    </el-dialog>
    <markmap-dialog
      :visible.sync="markmapVisible"
      :content="markmapContent"
      :title="markmapTitle"
      :loading="aiLoading"
      @close="handleMarkmapClose"
    />

    <!-- 技术领域泡泡图弹窗 -->
    <techBubbleDialog
      :visible="techBubbleDialogVisible"
      :title="techBubbleDialogTitle"
      :screenSn="techBubbleDialogScreenSn"
      @update:visible="techBubbleDialogVisible = $event"
      @openTechnologyDetails="openTechnologyDetails"
    />
  </div>
</template>

<script>
import usaMap from "./components/usaMap";
import timeLine from "./components/timeLine";
import graphEcharts from "./components/graphEcharts";
import technologyArticles from "./components/technologyArticles";
import trumpViewTree from "./components/trumpViewTree";
import viewTree from "./components/viewTree";
import policyRisk from "./secondLevel/policyRisk";
import articleDetails from "./secondLevel/articleDetails";
import suppressionOfRisks from "./secondLevel/suppressionOfRisks";
import enterpriseInformation from "./secondLevel/enterpriseInformation";
import comparisonChart from "./secondLevel/comparisonChart";
import hotTechnology from "./secondLevel/hotTechnology";
import baarTreeEcharts from "./components/baarTreeEcharts";
import technologyDetails from "./secondLevel/technologyDetails";
import techBubbleDialog from "./secondLevel/techBubbleDialog";
import ArticleNotification from "@/components/ArticleNotification";
import {
  technicalArticleDetail,
  suppressData,
  suppressLevelCount,
  suppressEnterpriseList,
  suppressPatentList,
  suppressSoftwareList,
  proposalsList,
  proposalsToChinaData,
  proposalsCount,
  kjdtArticleList,
  loginTCES,
  loginSINA,
} from "@/api/bigScreen/sanhao.js";
import {
  largeHotQueryById,
  largeGatherQueryGatherData,
  getLargeFTT,
  largeHotList2,
} from "@/api/bigScreen/index1";
import { markObj } from "./data/zhiku.js";
import { treeData2, markdownData } from "./data/renwu.js";
import MarkmapDialog from "../bigScreenThree/components/MarkmapDialog.vue";
import {
  containsHtmlTags,
  extractHtmlTags,
  hasValidHtmlStructure,
} from "@/utils/htmlUtils";
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";

export default {
  name: "TabOne",
  components: {
    usaMap,
    timeLine,
    graphEcharts,
    technologyArticles,
    trumpViewTree,
    viewTree,
    policyRisk,
    articleDetails,
    suppressionOfRisks,
    enterpriseInformation,
    comparisonChart,
    hotTechnology,
    baarTreeEcharts,
    technologyDetails,
    techBubbleDialog,
    MarkmapDialog,
    ArticleNotification,
  },
  props: {
    notificationArticles: {
      type: Array,
      default: () => [],
    },
    showNotification: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      policyRiskShowModal: false,
      comparisonChartShowModal: false,
      hotTechnologyShowModal: false,
      hotTechnologytTitle: "",
      hotTechnologytID: null,
      baarTreeEchartsShowModal: false,
      baarTreeEchartsType: null,
      technologyDetailsShowModal: false,
      technologyDetailsTitle: "",
      technologyDetailsItem: null,
      suppressionOfRisksShowModal: false,
      enterpriseInformationShowModal: false,
      enterpriseInformationTitle: "",
      articleDetailsShowModal: false,
      articleDetailsTitle: "",
      articleDetailsContent: "",
      articleDetailsContentEn: "",
      suppressListData: [],
      riskBarChartData: [],
      riskEnterpriseList: [],
      riskEnterpriseListTotal: 0,
      enterpriseInformationContent: {},
      patentList: [],
      softwareList: [],
      patentTotal: 0,
      softwareTotal: 0,
      policyRiskList1: [],
      policyRiskList2: [],
      policyRiskList1Total: 0,
      // 美国地图数据
      usaMapData: null,
      articleDetailsItem: {},
      // 热点推荐相关数据
      remengwenzhangList: [],
      scrollTimer: null,
      scrollTimer1: null,
      scrollTimer2: null,
      isHovered: false,
      isHovered1: false,
      isHovered2: false,
      scrollStep: 1,
      drawerInfo: {},
      articleDialogVisible: false,
      fontSize: 16,
      oriFontSize: 20,
      // 人物观点数据
      characterViewData: [],
      // 智库观点数据
      thinkTankViewData: [],
      remengwenzhangList1: [],
      activeButton: null,
      qianyankejiList: [],
      gatherTotal: 0,
      gatherDayNumber: 0,
      markmapVisible: false,
      markmapContent: "",
      markmapTitle: "智库观点",
      aiLoading: false,
      frontLoginParams: {
        username: "guanliyuan",
        password: "123456",
      },
      frontToken: "",
      // 技术领域相关
      activeTechButton: "11", // 默认选中新能源
      currentTechScreenSn: "11", // 当前技术领域的screenSn
      // 技术领域泡泡图弹窗相关
      techBubbleDialogVisible: false,
      techBubbleDialogTitle: "",
      techBubbleDialogScreenSn: "",
      // 智库观点tab切换
      activeTab: "trump", // 默认显示特朗普tab
      domainMarkdown: "",
      sinaUrl: "",
      zhikuActive: 0,
    };
  },
  computed: {
    // 动态计算热门文章列表框的样式
    remengwenzhangBoxStyle() {
      const notificationHeight = 110; // 通知组件的高度

      if (this.showNotification) {
        return {
          height: `calc(100% - ${notificationHeight}px)`,
        };
      } else {
        return {
          height: `100%`,
        };
      }
    },
  },
  mounted() {
    // 调用登录TCES接口
    loginTCES()
      .then(() => {
        console.log("TCES登录成功");
      })
      .catch((error) => {
        console.error("TCES登录失败:", error);
      });

    // 调用登录新浪接口
    loginSINA()
      .then((res) => {
        console.log("新浪登录成功");
        this.sinaUrl = res;
      })
      .catch((error) => {
        console.error("新浪登录失败:", error);
      });

    this.getSuppressData();
    this.initHotList();
    this.initHotList1();
    this.updateScrollbar();
    this.updateScrollbar1();
    this.fetchUsaMapData();
    largeGatherQueryGatherData({}).then((res) => {
      this.gatherTotal = res.data.gatherTotal;
      this.gatherDayNumber = res.data.gatherDayNumber;
    });
  },
  beforeDestroy() {
    this.clearScrollTimer();
    this.clearScrollTimer1();
    this.clearScrollTimer2();
    this.handleMarkmapClose();
  },
  methods: {
    // 获取美国地图数据
    async fetchUsaMapData() {
      try {
        const response = await proposalsCount({
          projectSn: "1",
          screenSn: "1",
          columnSn: "1",
        });
        this.usaMapData = response;
      } catch (error) {
        console.error("获取美国地图数据失败:", error);
      }
    },

    openArticleDetails(type, item) {
      this.articleDetailsItem = item;
      switch (type) {
        case "technology-article":
          technicalArticleDetail({ id: item.id }).then((res) => {
            this.articleDetailsTitle = item.title;
            this.articleDetailsContent = res.data.content;
            this.articleDetailsContentEn = res.data.enContent;
            this.articleDetailsShowModal = true;
          });
          break;
        case "enterpriseInformation-news":
          this.openNewView(item);
          break;
        case "policyRisk-news":
          this.openNewView(item);
          break;
      }
    },
    openEnterpriseInformation(item) {
      suppressPatentList({
        suppressSn: item.suppressSn,
        pageNum: 1,
        pageSize: 10,
      }).then((res) => {
        this.patentList = res.rows;
        this.patentTotal = res.total;
      });
      suppressSoftwareList({
        suppressSn: item.suppressSn,
        pageNum: 1,
        pageSize: 10,
      }).then((res) => {
        this.softwareList = res.rows;
        this.softwareTotal = res.total;
      });
      this.enterpriseInformationContent = { ...item };
      this.enterpriseInformationTitle = item.enterpriseName;
      this.enterpriseInformationShowModal = true;
    },

    patentPagination(suppressSn, queryParams) {
      suppressPatentList({
        suppressSn: suppressSn,
        ...queryParams,
      }).then((res) => {
        this.patentList = res.rows;
      });
    },

    softwarePagination(suppressSn, queryParams) {
      suppressSoftwareList({
        suppressSn: suppressSn,
        ...queryParams,
      }).then((res) => {
        this.softwareList = res.rows;
      });
    },

    getSuppressData() {
      suppressData({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
      }).then((res) => {
        let data = [];
        Object.keys(res.data).forEach((key) => {
          data.push({
            date: key,
            description:
              res.data[key].length <= 3
                ? res.data[key]
                : res.data[key].slice(
                    res.data[key].length - 3,
                    res.data[key].length
                  ),
          });
        });
        this.suppressListData = data.reverse();
      });
    },

    getRiskDetail() {
      suppressEnterpriseList({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
        pageNum: 1,
        pageSize: 10,
      }).then((res) => {
        this.riskEnterpriseList = res.rows.map((item, index) => ({
          ...item,
          type: (index % 3) + 1,
        }));
        this.riskEnterpriseListTotal = res.total;
      });
      suppressLevelCount({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
      }).then((res) => {
        // 将对象格式转换为数组格式
        const data = Object.keys(res.data).map((year) => ({
          product: year,
          严重: res.data[year].严重,
          一般: res.data[year].一般,
          较轻: res.data[year].较轻,
        }));
        this.riskBarChartData = data;
        this.suppressionOfRisksShowModal = true;
      });
    },

    riskEnterpriseListPagination(queryParams) {
      suppressEnterpriseList({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
        ...queryParams,
      }).then((res) => {
        this.riskEnterpriseList = res.rows.map((item, index) => ({
          ...item,
          type: (index % 3) + 1,
        }));
      });
    },

    getPolicyRiskDetail() {
      proposalsList({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
        pageNum: 1,
        pageSize: 10,
      }).then((res) => {
        this.policyRiskList1 = res.rows;
        this.policyRiskList1Total = res.total;
      });
      proposalsToChinaData({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
      }).then((res) => {
        this.policyRiskList2 = res.data;
        this.policyRiskShowModal = true;
      });
    },

    policyRiskPagination(queryParams) {
      proposalsList({
        projectSn: "1",
        screenSn: "1",
        columnSn: "1",
        ...queryParams,
      }).then((res) => {
        this.policyRiskList1 = res.rows;
      });
    },

    openHotTechnology(data) {
      this.hotTechnologyShowModal = true;
      this.hotTechnologytTitle = data.title;
      this.hotTechnologytID = data.reportSn;
    },
    openbaarTreeEcharts(data) {
      // this.baarTreeEchartsType = parseInt(this.$refs.trumpViewTree.nodeType.replace(/\D+/g, ''), 10)
      // this.baarTreeEchartsShowModal = true;

      // 根据当前选中的tab来决定使用哪个markObj
      if (this.activeTab === "trump") {
        // 使用导入的markdownData
        this.markmapContent =
          markdownData.trump[this.$refs.characterViewTree.nodeType];
        this.markmapTitle = "特朗普";
      } else if (this.activeTab === "msk") {
        // 马斯克的markdownData
        this.markmapContent =
          markdownData.msk[this.$refs.characterViewTree.nodeType];
        this.markmapTitle = "埃隆·里夫·马斯克";
      } else if (this.activeTab === "ws") {
        // 万斯的markdownData
        this.markmapContent =
          markdownData.ws[this.$refs.characterViewTree.nodeType];
        this.markmapTitle = "詹姆斯·唐纳德·万斯";
      } else {
        this.markmapContent = this.domainMarkdown;
        switch (this.activeTab) {
          case "bdt":
            this.markmapTitle = "半导体领域";
            break;
          case "gdzb":
            this.markmapTitle = "高端装备与材料";
            break;
          case "xnyqc":
            this.markmapTitle = "新能源汽车与电池";
            break;
          case "szhzx":
            this.markmapTitle = "数字化转型与工业软件";
            break;
          case "lszz":
            this.markmapTitle = "绿色制造与新能源";
            break;
          case "swyy":
            this.markmapTitle = "生物医药与医疗器械";
            break;
        }
      }
      this.aiLoading = false;
      this.markmapVisible = true;
    },

    openTechnologyDetails(data) {
      this.technologyDetailsShowModal = true;
      this.technologyDetailsTitle = data.name;
      this.technologyDetailsItem = data.data;
    },

    // 热点推荐相关方法
    initHotList() {
      // 使用bigScreenThree相同的API接口
      largeHotList2()
        .then((res) => {
          this.remengwenzhangList = res.data || [];
          this.$nextTick(() => {
            this.startScroll();
          });
        })
        .catch((error) => {
          console.error("获取热点推荐数据失败:", error);
          // 如果API调用失败，使用空数组
          this.remengwenzhangList = [];
        });
    },
    initHotList1() {
      this.handleButtonClick("脑机接口");
      this.$nextTick(() => {
        this.startScroll2();
      });
    },

    startScroll() {
      this.clearScrollTimer();
      const wrapper = this.$refs.scrollWrapper;
      const content = this.$refs.scrollContent;

      if (!wrapper || !content) return;

      this.scrollTimer = setInterval(() => {
        if (this.isHovered) return;

        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {
          wrapper.scrollTop = 0;
        } else {
          wrapper.scrollTop += this.scrollStep;
        }
        this.updateScrollbar();
      }, 40);
    },

    clearScrollTimer() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },

    handleMouseEnter() {
      this.isHovered = true;
    },

    handleMouseLeave() {
      this.isHovered = false;
      this.startScroll();
    },
    startScroll1() {
      this.clearScrollTimer1();
      const wrapper = this.$refs.scrollWrapper1;
      const content = this.$refs.scrollContent1;

      if (!wrapper || !content) return;

      this.scrollTimer1 = setInterval(() => {
        if (this.isHovered1) return;

        if (wrapper.scrollTop >= content.scrollHeight - wrapper.clientHeight) {
          wrapper.scrollTop = 0;
        } else {
          wrapper.scrollTop += this.scrollStep;
        }
        this.updateScrollbar1();
      }, 20);
    },
    startScroll2() {
      this.clearScrollTimer2();
      this.scrollTimer2 = setInterval(() => {
        if (this.isHovered1) return;

        // 定义所有tab标签的顺序
        const tabOrder = [
          "脑机接口",
          "量子信息",
          "人形机器人",
          "生成式人工智能",
          "生物制造",
          "未来显示",
          "未来网络",
          "新型储能",
          "其他",
        ];

        // 找到当前活跃标签的索引
        const currentIndex = tabOrder.indexOf(this.activeButton);
        // 计算下一个标签的索引，如果到最后一个则回到第一个
        const nextIndex = (currentIndex + 1) % tabOrder.length;
        // 切换到下一个标签
        this.handleButtonClick(tabOrder[nextIndex]);
      }, 8000);
    },
    clearScrollTimer1() {
      if (this.scrollTimer1) {
        clearInterval(this.scrollTimer1);
        this.scrollTimer1 = null;
      }
    },
    clearScrollTimer2() {
      if (this.scrollTimer2) {
        clearInterval(this.scrollTimer2);
        this.scrollTimer2 = null;
      }
    },
    handleMouseEnter1() {
      this.isHovered1 = true;
    },

    handleMouseLeave1() {
      this.isHovered1 = false;
      // this.startScroll1();
      // this.startScroll2();
    },
    handleMouseEnter2() {
      this.isHovered2 = true;
    },

    handleMouseLeave2() {
      this.isHovered2 = false;
    },
    updateScrollbar() {
      const wrapper = this.$refs.scrollWrapper;
      if (!wrapper) return;

      const { scrollTop, scrollHeight, clientHeight } = wrapper;
      const scrollPercent = clientHeight / scrollHeight;
      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);
      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;

      document.documentElement.style.setProperty(
        "--scrollbar-height",
        `${scrollbarHeight}px`
      );
      document.documentElement.style.setProperty(
        "--scrollbar-top",
        `${scrollbarTop}px`
      );
    },
    updateScrollbar1() {
      const wrapper = this.$refs.scrollWrapper1;
      if (!wrapper) return;

      const { scrollTop, scrollHeight, clientHeight } = wrapper;
      const scrollPercent = clientHeight / scrollHeight;
      const scrollbarHeight = Math.max(30, scrollPercent * clientHeight);
      const scrollbarTop = (scrollTop / scrollHeight) * clientHeight;

      document.documentElement.style.setProperty(
        "--scrollbar-height",
        `${scrollbarHeight}px`
      );
      document.documentElement.style.setProperty(
        "--scrollbar-top",
        `${scrollbarTop}px`
      );
    },

    async openNewView(item) {
      // 使用bigScreenThree相同的API接口
      try {
        const res = await largeHotQueryById(item.id);
        this.drawerInfo = {
          cnTitle:
            item.cnTitle || item.title || res.data.title || res.data.cnTitle,
          title:
            item.title || item.cnTitle || res.data.title || res.data.cnTitle,
          cnContent: res.data.content || res.data.cnContent,
        };

        // 处理内容格式
        let content = this.formattingJson(this.drawerInfo.cnContent);
        // if (content) {
        //   content = content.replace(/\n/g, "<br>");
        //   content = content.replace(/\${[^}]+}/g, "<br>");
        //   content = content.replace("|xa0", "");
        //   content = content.replace("opacity: 0", "");
        //   content = content.replace(/<img\b[^>]*>/gi, "");
        //   content = content.replace(/ style="[^"]*"/g, "");
        // }
        this.drawerInfo.cnContent = content;

        this.articleDialogVisible = true;
        this.oriFontSize = this.fontSize;
      } catch (error) {
        console.error("获取文章详情失败:", error);
        // 如果API调用失败，显示基本信息
        this.drawerInfo = {
          cnTitle: item.title || item.cnTitle,
          title: item.title || item.cnTitle,
          cnContent: "暂无详细内容",
        };
        this.articleDialogVisible = true;
        this.oriFontSize = this.fontSize;
      }
    },
    async openNewView1(item) {
      // 使用bigScreenThree相同的API接口
      try {
        const res = await getLargeFTT(item.sn);
        this.drawerInfo = {
          cnTitle:
            item.cnTitle || item.title || res.data.title || res.data.cnTitle,
          title:
            item.title || item.cnTitle || res.data.title || res.data.cnTitle,
          cnContent: res.data.content || res.data.cnContent,
        };

        // 处理内容格式
        let content = this.formattingJson(this.drawerInfo.cnContent);
        // if (content) {
        //   content = content.replace(/\n/g, "<br>");
        //   content = content.replace(/\${[^}]+}/g, "<br>");
        //   content = content.replace("|xa0", "");
        //   content = content.replace("opacity: 0", "");
        //   content = content.replace(/<img\b[^>]*>/gi, "");
        //   content = content.replace(/ style="[^"]*"/g, "");
        // }
        this.drawerInfo.cnContent = content;

        this.articleDialogVisible = true;
        this.oriFontSize = this.fontSize;
      } catch (error) {
        console.error("获取文章详情失败:", error);
        // 如果API调用失败，显示基本信息
        this.drawerInfo = {
          cnTitle: item.title || item.cnTitle,
          title: item.title || item.cnTitle,
          cnContent: "暂无详细内容",
        };
        this.articleDialogVisible = true;
        this.oriFontSize = this.fontSize;
      }
    },
    formattingJson(content) {
      if (content) {
        if (containsHtmlTags(content)) {
          content = content.replace(/<br>/g, "");
          content = content.replace(/\n/g, "");
          content = content.replace(/\\n/g, "");
          content = content.replace(/\\\n/g, "");
          content = content.replace("|xa0", "");
          content = content.replace("opacity: 0", "");
          // content = content.replace(/\${[^}]+}/g, "");
          content = content.replace(/<img\b[^>]*>/gi, "");
          // 移除带样式的标签，保留内容
          content = content.replace(
            /<(\w+)[^>]*style="[^"]*"[^>]*>(.*?)<\/\1>/gi,
            "$2"
          );
          // 移除任何其他样式标签
          content = content.replace(
            /<(\w+)[^>]*class="[^"]*"[^>]*>(.*?)<\/\1>/gi,
            "$2"
          );

          console.log("包含的HTML标签", extractHtmlTags(content));
          console.log("HTML是否结构正确", hasValidHtmlStructure(content));
        } else {
          content = content.replace(/\n/g, "<br>");
          content = content.replace(/\\n/g, "<br>");
          content = content.replace(/\\\n/g, "<br>");
          content = content.replace(/\${[^}]+}/g, "<br>");
          content = content.replace("|xa0", "");
          content = content.replace("opacity: 0", "");
          content = content.replace(/<img\b[^>]*>/gi, "");
          content = content.replace(
            /<(\w+)[^>]*style="[^"]*"[^>]*>(.*?)<\/\1>/gi,
            "$2"
          );
          // 移除任何其他样式标签
          content = content.replace(
            /<(\w+)[^>]*class="[^"]*"[^>]*>(.*?)<\/\1>/gi,
            "$2"
          );
        }
      }
      return content;
    },

    handleClose() {
      this.drawerInfo = {};
      this.articleDialogVisible = false;
    },

    increaseFontSize() {
      if (this.fontSize < 30) {
        this.fontSize += 2;
      }
    },

    decreaseFontSize() {
      if (this.fontSize > 16) {
        this.fontSize -= 2;
      }
    },
    handleNodeClick(type) {
      // 根据当前activeTab获取对应人物的数据
      let currentCharacter = "trump"; // 默认特朗普
      if (this.activeTab === "msk") {
        currentCharacter = "msk";
      } else if (this.activeTab === "ws") {
        currentCharacter = "ws";
      }
      let rawData = JSON.parse(
        JSON.stringify(treeData2[currentCharacter][type] || [])
      );
      this.characterViewData = this.limitLevel3Children(rawData);
    },
    limitLevel3Children(data) {
      if (!data || !Array.isArray(data)) return data;
      return data.map((item) => {
        if (
          (item.type == "level2-1" ||
            item.type == "level2-2" ||
            item.type == "level2-3") &&
          Array.isArray(item.children)
        ) {
          item.children = item.children.slice(0, 2); // 只保留前两个
        }

        if (item.children) {
          item.children = this.limitLevel3Children(item.children);
        }

        return item;
      });
    },
    handleButtonClick(type) {
      let obj = {
        脑机接口: "3",
        量子信息: "4",
        人形机器人: "6",
        生成式人工智能: "1",
        生物制造: "7",
        未来显示: "8",
        未来网络: "9",
        新型储能: "10",
        其他: "2,5,11,12,13,14,15,16,17",
      };
      this.activeButton = type;

      // 重置轮播时间
      this.startScroll2();

      kjdtArticleList({
        labelSn: obj[type],
      }).then((res) => {
        // 对数据进行去重处理，基于cnTitle去除空格后判断
        const deduplicatedData = this.deduplicateArticles(res || []);
        this.remengwenzhangList1 = deduplicatedData;
        this.$nextTick(() => {
          const wrapper = this.$refs.scrollWrapper1;
          wrapper.scrollTop = 0;
        });
      });
    },
    qykjdtOpenNewTab() {
      let obj = {
        脑机接口: "/qianyankejidongtai/naojijiekou?id=1&domain=3",
        量子信息: "/qianyankejidongtai/liangzixinxi?id=1&domain=4",
        人形机器人: "/qianyankejidongtai/renxingjiqiren?id=1&domain=6",
        生成式人工智能: "/qianyankejidongtai/rengongzhineng?id=1&domain=1",
        生物制造: "/qianyankejidongtai/shengwuzhizao?id=1&domain=7",
        未来显示: "/qianyankejidongtai/weilaixianshi?id=1&domain=8",
        未来网络: "/qianyankejidongtai/weilaiwangluo?id=1&domain=9",
        新型储能: "/qianyankejidongtai/xinxingchuneng?id=1&domain=10",
        其他: "/qianyankejidongtai/qita?id=1&domain=2,5,11,12,13,14,15,16,17",
      };
      window.open(obj[this.activeButton], "_blank");
    },
    // 文章去重方法，基于cnTitle去除空格后判断
    deduplicateArticles(articles) {
      if (!Array.isArray(articles)) {
        return [];
      }

      const seen = new Set();
      const result = [];

      articles.forEach((article) => {
        if (article && article.cnTitle) {
          // 去除cnTitle中的所有空格
          const normalizedTitle = article.cnTitle.replace(/\s+/g, "");

          if (!seen.has(normalizedTitle)) {
            seen.add(normalizedTitle);
            result.push(article);
          }
        } else {
          // 如果没有cnTitle，也保留这条记录
          result.push(article);
        }
      });

      return result;
    },
    padWithZeros(num, targetLength) {
      const numStr = num.toString();
      const padding = "0".repeat(targetLength - numStr.length);
      return `${padding}${numStr}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    openNewTab(url) {
      window.open(url, "_blank");
    },
    handleMarkmapClose() {
      this.markmapContent = "";
      this.aiLoading = false;
      this.markmapVisible = false;
    },

    // 更新热点推荐文章列表
    async updateHotArticlesList() {
      try {
        const response = await largeHotList2();
        if (response && response.data) {
          const newArticles = response.data;
          // 对比数据是否一致
          if (this.isArticleDataChanged(newArticles)) {
            this.remengwenzhangList = newArticles;
          } else {
          }
        }
      } catch (error) {
        console.error("更新热点推荐文章列表失败:", error);
      }
    },

    // 检查文章数据是否发生变化
    isArticleDataChanged(newArticles) {
      // 如果当前列表为空，直接返回true
      if (this.remengwenzhangList.length === 0) {
        return newArticles.length > 0;
      }

      // 如果数量不同，说明有变化
      if (this.remengwenzhangList.length !== newArticles.length) {
        return true;
      }

      // 对比每篇文章的关键信息
      for (let i = 0; i < newArticles.length; i++) {
        const newArticle = newArticles[i];
        const oldArticle = this.remengwenzhangList[i];

        // 对比文章ID、标题、发布时间等关键字段
        if (
          newArticle.id !== oldArticle.id ||
          newArticle.title !== oldArticle.title ||
          newArticle.publishTime !== oldArticle.publishTime ||
          newArticle.sourceName !== oldArticle.sourceName
        ) {
          return true;
        }
      }

      // 所有数据都一致
      return false;
    },

    // 处理技术领域按钮点击
    handleTechButtonClick(screenSn, buttonName) {
      console.log("切换技术领域:", buttonName, "screenSn:", screenSn);
      this.activeTechButton = screenSn;
      this.currentTechScreenSn = screenSn;
      // 弹出技术领域泡泡图弹窗
      this.techBubbleDialogVisible = true;
      this.techBubbleDialogTitle = buttonName;
      this.techBubbleDialogScreenSn = screenSn;

      // 通知子组件更新数据
      this.$nextTick(() => {
        // 可以通过ref直接调用子组件的方法来刷新数据
        // 或者通过watch监听currentTechScreenSn的变化来触发子组件更新
      });
    },

    // 关闭技术领域泡泡图弹窗
    handleTechBubbleDialogClose() {
      this.techBubbleDialogVisible = false;
      this.techBubbleDialogTitle = "";
      this.techBubbleDialogScreenSn = "";
    },

    // 处理通知关闭
    handleNotificationClose() {
      this.$emit("notification-close");
    },

    // 处理查看单篇文章
    handleViewArticle(article) {
      this.$emit("notification-view-article", article);
    },

    // 切换智库观点tab
    switchTab(tabName, markdownType) {
      this.activeTab = tabName;
      if (markdownType) {
        this.domainMarkdown = markObj["type" + markdownType];
        this.renderMarkmap();
      } else if (tabName === "trump" || tabName === "msk" || tabName === "ws") {
        // 切换到人物tab时，清空之前的数据，等待组件初始化后自动加载
        this.characterViewData = [];
      }
    },
    async renderMarkmap() {
      if (!this.domainMarkdown) {
        this.loading = false;
        return;
      }

      try {
        await this.$nextTick();
        const svg = this.$refs.markmap;
        if (!svg) {
          throw new Error("SVG element not found");
        }

        // 清空之前的内容
        svg.innerHTML = "";

        // 处理内容，移除 markdown 标记
        let processedContent = this.domainMarkdown
          .replace(/^```markdown\s*/i, "") // 移除开头的 ```markdown
          .replace(/\s*```\s*$/, ""); // 移除结尾的 ```

        const transformer = new Transformer();
        const { root } = transformer.transform(processedContent);

        // 创建思维导图
        const mm = Markmap.create(
          svg,
          {
            autoFit: true,
            duration: 0,
            nodeMinHeight: 20,
            spacingVertical: 10,
            spacingHorizontal: 100,
            paddingX: 20,
            color: (node) => {
              const colors = {
                0: "#0052ff", // 亮蓝色
                1: "#009600", // 亮绿色
                2: "#ff6600", // 亮橙色
                3: "#8000ff", // 亮紫色
                4: "#ff0066", // 亮粉色
              };
              return colors[node.depth] || "#0052ff";
            },
            nodeFont: (node) => {
              const fonts = {
                0: 'bold 20px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                1: '600 18px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
                2: '500 16px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
              };
              return (
                fonts[node.depth] ||
                '400 14px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto'
              );
            },
            maxWidth: 400,
            initialExpandLevel: -1,
            zoom: true,
            pan: true,
            linkShape: "diagonal",
            linkWidth: (node) => 2.5 - node.depth * 0.5,
            linkColor: (node) => {
              const colors = {
                0: "rgba(0, 82, 255, 0.8)", // 亮蓝色
                1: "rgba(0, 150, 0, 0.8)", // 亮绿色
                2: "rgba(255, 102, 0, 0.8)", // 亮橙色
              };
              return colors[node.depth] || "rgba(128, 0, 255, 0.8)";
            },
          },
          root
        );

        // 修改初始化动画部分
        setTimeout(() => {
          mm.fit(); // 适应视图大小

          // 重新设置数据以触发重绘
          const fitRatio = 0.95; // 留出一些边距
          const { minX, maxX, minY, maxY } = mm.state;
          const width = maxX - minX;
          const height = maxY - minY;
          const containerWidth = svg.clientWidth;
          const containerHeight = svg.clientHeight;

          // 计算合适的缩放比例
          const scale = Math.min(
            (containerWidth / width) * fitRatio,
            (containerHeight / height) * fitRatio
          );

          // 更新数据以应用新的缩放
          mm.setData(root, {
            initialScale: scale,
            initialPosition: [
              (containerWidth - width * scale) / 2,
              (containerHeight - height * scale) / 2,
            ],
          });
        }, 100);

        // 监听窗口大小变化
        const resizeHandler = () => mm.fit();
        window.addEventListener("resize", resizeHandler);

        // 组件销毁时清理
        this.$once("hook:beforeDestroy", () => {
          window.removeEventListener("resize", resizeHandler);
        });
      } catch (error) {
        console.error("Markmap rendering error:", error);
        this.$message.error("思维导图渲染失败");
      } finally {
        this.loading = false;
      }
    },
    // 打开新浪舆情通
    openSina() {
      window.open(this.sinaUrl, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.two {
  height: 100%;
  width: 100%;
  padding-bottom: 10px;

  .left {
    width: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .center {
    margin: 0 11px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .center-top {
      height: 150px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      .top-content {
        position: relative;
        width: 315px;
        height: 98px;
        background-image: url("../../assets/bigScreenSanhao/centerBg1.png");
        background-size: 100% 100%;
      }

      .bg1 {
        position: absolute;
        top: 17px;
        left: 43px;
        width: 60px;
        height: 60px;
        background-image: url("../../assets/bigScreenSanhao/centerBg2.png");
        background-size: 100% 100%;
      }

      .top-content-number {
        font-size: 24px;
        color: #00e5ff;
        position: absolute;
        left: 138px;
        top: 44px;
      }

      .top-content-name {
        font-size: 18px;
        color: #ffffff;
        position: absolute;
        left: 138px;
        top: 22px;
      }
    }
  }

  .right {
    width: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .bsContentBox2 {
      background-image: url("../../assets/bigScreenSanhao/contentBg1.png");

      .bsContentContent {
        height: calc((100% - 43px) / 2);
      }

      .kejidongtai-box {
        margin-top: 0;
      }
    }
  }

  .bsContentBox,
  .bsContentBox2 {
    background-image: url("../../assets/bigScreenSanhao/contentBg.png");
    background-size: 100% 100%;

    .bsContentTitle {
      height: 43px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      position: relative;

      .bsContentTitleIcon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        background-image: url("../../assets/bigScreenSanhao/titleLogo.png");
        background-size: 100% 100%;
      }

      .bsContentTitleName {
        height: 43px;
        line-height: 43px;
        font-weight: 800;
        font-size: 20px;
        color: #00abf4;
      }

      .bsContentTitleHelp {
        cursor: pointer;
        width: 21px;
        height: 21px;
        margin-left: 10px;
        background-image: url("../../assets/bigScreenSanhao/titleHelp.png");
        background-size: 100% 100%;
      }

      .bsContentTitleMore {
        position: absolute;
        right: 10px;
        cursor: pointer;
        width: 70px;
        height: 43px;
        line-height: 60px;
        font-size: 17px;
        color: #00c8ff;

        &:after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 22px;
          width: 18px;
          height: 18px;
          margin-left: 5px;
          background-image: url("../../assets/bigScreenSanhao/jiantou.png");
          background-size: 100% 100%;
        }
      }
    }

    .bsContentContent {
      height: calc(100% - 43px);
    }
  }

  .bsContentBox1 {
    flex: 1;

    .bsContentTitle1 {
      height: 43px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      background-image: url("../../assets/bigScreenSanhao/title1.png");
      background-size: 100% 100%;
      position: relative;

      .bsContentTitleIcon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
        background-image: url("../../assets/bigScreenSanhao/titleLogo.png");
        background-size: 100% 100%;
      }

      .bsContentTitleName {
        height: 43px;
        line-height: 43px;
        font-weight: 800;
        font-size: 20px;
        color: #00abf4;

        span {
          font-weight: normal;
          cursor: pointer;
          color: rgba(0, 171, 244, 0.5);
        }

        .titleColor {
          font-weight: 800;
          color: #ffffff;
        }
      }

      .bsContentTitleHelp {
        cursor: pointer;
        width: 21px;
        height: 21px;
        margin-left: 10px;
        background-image: url("../../assets/bigScreenSanhao/titleHelp.png");
        background-size: 100% 100%;
      }

      .bsContentTitleMore {
        position: absolute;
        right: 10px;
        cursor: pointer;
        width: 70px;
        height: 43px;
        line-height: 60px;
        font-size: 17px;
        color: #00c8ff;

        &:after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 22px;
          width: 18px;
          height: 18px;
          margin-left: 5px;
          background-image: url("../../assets/bigScreenSanhao/jiantou.png");
          background-size: 100% 100%;
        }
      }
    }

    .bsContentContent {
      height: calc(100% - 43px);
      display: flex;
      flex-direction: column;
      position: relative;

      .trump-view-container {
        height: 300px;
      }

      .view-tree-container {
        flex: 1;
        display: flex;
        flex-direction: column;

        & > div {
          flex: 1;
        }
      }

      .bsContentTitleMore {
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 99;
        cursor: pointer;
        width: 70px;
        height: 43px;
        line-height: 60px;
        font-size: 17px;
        color: #00c8ff;

        &:after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 22px;
          width: 18px;
          height: 18px;
          margin-left: 5px;
          background-image: url("../../assets/bigScreenSanhao/jiantou.png");
          background-size: 100% 100%;
        }
      }

      // Tab 按钮样式
      .tab-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .tab-button {
          padding: 8px 8px;
          background: rgba(0, 171, 244, 0.2);
          border: 1px solid rgba(0, 171, 244, 0.5);
          border-radius: 4px;
          color: rgba(0, 171, 244, 0.8);
          cursor: pointer;
          font-size: 14px;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 171, 244, 0.3);
            color: #00abf4;
          }

          &.active {
            background: rgba(0, 171, 244, 0.5);
            color: #ffffff;
            border-color: #00abf4;
          }
        }
      }

      // Tab 内容样式
      .tab-content {
        flex: 1;
        display: flex;
        flex-direction: column;

        .trump-view-container {
          height: 300px;
        }

        .view-tree-container {
          flex: 1;
          display: flex;
          flex-direction: column;

          & > div {
            flex: 1;
          }
        }

        .markmap-svg {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
  }

  // 热点推荐滚动列表样式
  .remengwenzhang-box {
    width: 100%;
    height: 100%;
    padding: 20px;
    border: 1px solid rgba(16, 216, 255, 0.4);
    background: rgba(0, 0, 0, 0.15);
    box-shadow: 0px 0px 8px 0px #0056ad;
    overflow: hidden;
    position: relative;

    .scroll-wrapper {
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      position: relative;

      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 20px;
      right: 0;
      height: calc(100% - 40px);
      width: 6px;
      background: rgba(16, 216, 255, 0.1);
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
    }

    .scroll-bar {
      position: absolute;
      top: 20px;
      right: 0;
      width: 6px;
      height: var(--scrollbar-height, 100px);
      background: rgba(16, 216, 255, 0.4);
      border-radius: 3px;
      opacity: 0;
      transition: opacity 0.3s;
      transform: translateY(var(--scrollbar-top, 0));
      pointer-events: none;
    }

    &:hover {
      &::after,
      .scroll-bar {
        opacity: 1;
      }
    }

    .remengwenzhang-list {
      position: relative;
      height: 40px;
      padding-left: 20px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;

      .title {
        width: 330px;
        overflow: hidden;
        color: rgba(216, 240, 255, 0.8);
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: "Source Han Sans CN";
        font-size: 18px;
        font-weight: 700;
        line-height: 20px;
      }

      .time,
      .sourceName {
        width: 150px;
        color: rgba(216, 240, 255, 0.8);
        text-align: right;
        font-family: "Source Han Sans CN";
        font-size: 18px;
        font-weight: 400;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .block {
        position: absolute;
        left: 0px;
        top: 6px;
        width: 10px;
        height: 10px;
        border-radius: 1px;
        background: #1bdcff;
      }
    }
  }

  .remengwenzhang-box1 {
    width: 100%;
    height: 230px;
    padding: 20px;
    border: 1px solid rgba(16, 216, 255, 0.4);
    background: rgba(0, 0, 0, 0.15);
    box-shadow: 0px 0px 8px 0px #0056ad;
    overflow: hidden;
    position: relative;

    .scroll-wrapper {
      height: 100%;
      overflow-y: scroll;
      overflow-x: hidden;
      position: relative;

      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &::after {
      content: "";
      position: absolute;
      top: 20px;
      right: 0;
      height: calc(100% - 40px);
      width: 6px;
      background: rgba(16, 216, 255, 0.1);
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
    }

    .scroll-bar {
      position: absolute;
      top: 20px;
      right: 0;
      width: 6px;
      height: var(--scrollbar-height, 100px);
      background: rgba(16, 216, 255, 0.4);
      border-radius: 3px;
      opacity: 0;
      transition: opacity 0.3s;
      transform: translateY(var(--scrollbar-top, 0));
      pointer-events: none;
    }

    &:hover {
      &::after,
      .scroll-bar {
        opacity: 1;
      }
    }

    .remengwenzhang-list {
      position: relative;
      height: 40px;
      padding-left: 20px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;

      .title {
        width: 330px;
        overflow: hidden;
        color: rgba(216, 240, 255, 0.8);
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: "Source Han Sans CN";
        font-size: 18px;
        font-weight: 700;
        line-height: 20px;
      }

      .time,
      .sourceName {
        width: 150px;
        color: rgba(216, 240, 255, 0.8);
        text-align: right;
        font-family: "Source Han Sans CN";
        font-size: 18px;
        font-weight: 400;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .block {
        position: absolute;
        left: 0px;
        top: 6px;
        width: 10px;
        height: 10px;
        border-radius: 1px;
        background: #1bdcff;
      }
    }
  }
}

// 弹窗样式
::v-deep .el-dialog {
  background: url("../../assets/bigScreenTwo/dialogBackground.png") no-repeat;
  background-size: 100% 100% !important;
  background-size: cover;
  height: 800px;

  .el-dialog__header {
    background-color: #1d233400;
    font-size: 30px;
    color: #ffffff;
    line-height: 100px;
    text-shadow: 0px 0px 10px rgba(30, 198, 255, 0.8);
    height: 100px;

    .el-dialog__title {
      display: inline-block;
      width: calc(100% - 100px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-dialog__body {
    background-color: #2a304000;
    color: #f2f2f2;
    height: calc(100% - 140px);
    overflow: hidden;
    padding: 20px 30px;
  }

  .el-dialog__footer {
    background-color: #1d233400;
    padding: 18px 20px;
  }

  .el-button {
    background-color: #002766;
    color: #fff;
    border: 0px;
  }

  .el-dialog__headerbtn .el-dialog__close {
    background: url("../../assets/bigScreenTwo/关闭小.png") no-repeat;
    background-size: 100% 100% !important;
    background-size: cover;
    width: 31px;
    height: 31px;
    top: 16px;

    &::before {
      content: none;
    }
  }
}

.dialog-art {
  background: #1d293b;
  padding: 20px;
  height: 590px;
  overflow-y: auto;
  line-height: 1.8em;
  font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,
    Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei, Arial,
    sans-serif;

  ::v-deep p {
    text-indent: 2em;
  }
}

/* 自定义滚动条样式 */
::v-deep .dialog-art::-webkit-scrollbar {
  width: 8px;
}

::v-deep .dialog-art::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::v-deep .dialog-art::-webkit-scrollbar-thumb {
  background: rgba(14, 194, 244, 0.6);
  border-radius: 4px;
}

::v-deep .dialog-art::-webkit-scrollbar-thumb:hover {
  background: rgba(14, 194, 244, 0.8);
}

.fz {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 20px;

  .text {
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    margin-right: 10px;
  }

  .btns {
    display: flex;
    align-items: center;
    background: #1d293b;
    border-radius: 14px;
    padding: 0 10px;
    height: 28px;

    .btn-minus,
    .btn-plus {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 20px;
      color: #ffffff;

      &:hover {
        color: #2f7cfe;
      }
    }

    .font-size {
      margin: 0 15px;
      color: #ffffff;
      font-size: 16px;
      min-width: 45px;
      text-align: center;
    }
  }
}

.kejidongtai-box {
  width: 100%;
  // height: 45px;
  // padding-top: 11px;
  // margin: 3px 0;
  display: flex;
  // justify-content: space-around;
  flex-wrap: wrap;
  column-gap: 10px;
  row-gap: 10px;
  padding: 10px;
  margin-top: 10px;

  .kejidongtai-button {
    // width: 111px;
    height: 33px;
    line-height: 33px;
    text-align: center;
    font-size: 14px;
    color: #ffffff;
    background-image: url("../../assets/bigScreenSanhao/kejiqianyan1.png");
    background-size: 100% 100%;
    padding: 0 12px;
  }

  .active {
    background-image: url("../../assets/bigScreenSanhao/kejiqianyan2.png");
  }
}

:deep(.markmap-node) {
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

:deep(.markmap-node-circle) {
  fill: transparent; // 修改节点背景为透明
  stroke-width: 2px;
}

:deep(.markmap-node-text) {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial;

  tspan {
    fill: #333 !important; // 修改文字颜色为深色
    font-size: 14px;
    font-weight: 500;
  }
}

:deep(.markmap-link) {
  fill: none;
  stroke-width: 2.5px; // 加粗连线
}

// 根节点样式
:deep(.markmap-node[data-depth="0"]) {
  .markmap-node-circle {
    stroke: #0052ff; // 亮蓝色
    stroke-width: 3px;
  }

  .markmap-node-text tspan {
    font-size: 20px !important;
    font-weight: bold !important;
    fill: #333 !important;
  }
}

// 二级节点样式
:deep(.markmap-node[data-depth="1"]) {
  .markmap-node-circle {
    stroke: #009600; // 亮绿色
    stroke-width: 2.5px;
  }

  .markmap-node-text tspan {
    font-size: 18px !important;
    font-weight: 600 !important;
    fill: #333 !important;
  }
}

// 三级节点样式
:deep(.markmap-node[data-depth="2"]) {
  .markmap-node-circle {
    stroke: #ff6600; // 亮橙色
    stroke-width: 2px;
  }

  .markmap-node-text tspan {
    font-size: 16px !important;
    font-weight: 500 !important;
    fill: #333 !important;
  }
}

// 其他层级节点样式
:deep(.markmap-node[data-depth="3"]),
:deep(.markmap-node[data-depth="4"]) {
  .markmap-node-circle {
    stroke: #8000ff; // 亮紫色
    stroke-width: 2px;
  }

  .markmap-node-text tspan {
    font-size: 14px !important;
    font-weight: 500 !important;
    fill: #333 !important;
  }
}
</style>
