{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue", "mtime": 1753690547255}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyByZW5kZXJMaW5lQ2hhcnRzLCByZW5kZXJBbm51bGFyIH0gZnJvbSAiQC91dGlscy9yZW5kZXJMaW5lLmpzIjsNCmltcG9ydCB7IGFudGlTaGFrZSB9IGZyb20gIkAvdXRpbHMvdXRpbHMuanMiOw0KaW1wb3J0IHsgZm9ybWF0RGF0ZSB9IGZyb20gIkAvdXRpbHMvaW5kZXguanMiOw0KaW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICJlY2hhcnRzIjsNCmltcG9ydCBBUEkgZnJvbSAiQC9hcGkvU2NpZW5jZUFwaS9icmllZmluZy5qcyI7DQovLyDlvJXlhaVkb2N4LXByZXZpZXfmj5Lku7YNCmxldCBkb2N4ID0gcmVxdWlyZSgiZG9jeC1wcmV2aWV3Iik7DQpleHBvcnQgZGVmYXVsdCB7DQogIHByb3BzOiB7DQogICAgcHJlVmlld0RhdGE6IHsNCiAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgIH0sDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICBkb3dubG9hZGluZzogZmFsc2UsDQogICAgICBteUNoYXJ0OiBudWxsLA0KICAgICAgaW5mb0RhdGE6IHt9LA0KICAgICAgY29udGVudDogew0KICAgICAgICBkZXRlY3Rpb25UaW1lOiAiIiwNCiAgICAgIH0sDQogICAgICB4QXhpczoge30sDQogICAgICB0aW1lcjogbnVsbCwNCiAgICAgIHdlY2hhdExpc3Q6IFtdLA0KICAgIH07DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5mb3JtYXREYXRlID0gZm9ybWF0RGF0ZTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBpZiAodGhpcy5wcmVWaWV3RGF0YS5mbGFnKSB7DQogICAgICB0aGlzLmRvd25sb2FkQnJpZUZpbmcoKTsNCiAgICB9DQoNCiAgICAvKiDlvq7kv6HlhazkvJflj7fmqKHmnb8gKi8NCiAgICBpZiAodGhpcy5wcmVWaWV3RGF0YS5pc1dlY2hhdCA9PSAxKSB7DQogICAgICB0aGlzLlN0YXRpc3RpY2FsTGlzdCgpOw0KICAgIH0gZWxzZSBpZiAodGhpcy5wcmVWaWV3RGF0YS5pc1dlY2hhdCA9PSAyKSB7DQogICAgICB0aGlzLmdldFdvcmxkKCk7DQogICAgfSBlbHNlIHsNCiAgICAgIHRoaXMuZ2V0SW5mb0RhdGEoKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICByZW5kZXJMaW5lQ2hhcnRzKCkgew0KICAgICAgbGV0IGRhdGEgPSB0aGlzLmNocnRzRGF0YUhhbmRsZSgpOw0KICAgICAgbmV3IHJlbmRlckxpbmVDaGFydHMoew0KICAgICAgICBkb206ICJsaW5lIiwNCiAgICAgICAgZGF0YTogZGF0YSwNCiAgICAgICAgdGl0bGVTaG93OiBmYWxzZSwNCiAgICAgICAgeEF4aXM6IHRoaXMueEF4aXMsDQogICAgICB9KS5yZW5kZXIoKTsNCiAgICAgIC8vIHRoaXMucmVuZGVyQW5udWxhcigpDQogICAgfSwNCiAgICBhc3luYyBnZXRXb3JsZCgpIHsNCiAgICAgIGF3YWl0IEFQSS5leHBvcnRXb3JsZCh7IHJlcG9ydElkOiB0aGlzLnByZVZpZXdEYXRhLmJyaWVmaW5nSWQgfSkudGhlbigNCiAgICAgICAgKHJlcykgPT4gew0KICAgICAgICAgIGRvY3gucmVuZGVyQXN5bmMocmVzLCB0aGlzLiRyZWZzLmZpbGUpOyAvLyDmuLLmn5PliLDpobXpnaINCiAgICAgICAgICB0aGlzLiRzdG9yZS5jb21taXQoImFwcC9zZXRfTG9kaW5nIiwgZmFsc2UpOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgcmVuZGVyQmFyKCkgew0KICAgICAgdmFyIGNoYXJ0RG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoImJhciIpOw0KICAgICAgdGhpcy5teUNoYXJ0ID0gZWNoYXJ0cy5pbml0KGNoYXJ0RG9tKTsNCiAgICAgIHZhciBvcHRpb247DQogICAgICBvcHRpb24gPSB7DQogICAgICAgIHhBeGlzOiB7DQogICAgICAgICAgdHlwZTogInZhbHVlIiwNCiAgICAgICAgICBtYXg6IDI1MCwNCiAgICAgICAgfSwNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICJheGlzIiwNCiAgICAgICAgICBheGlzUG9pbnRlcjogew0KICAgICAgICAgICAgdHlwZTogInNoYWRvdyIsDQogICAgICAgICAgfSwNCiAgICAgICAgfSwNCiAgICAgICAgeUF4aXM6IHsNCiAgICAgICAgICB0eXBlOiAiY2F0ZWdvcnkiLA0KICAgICAgICAgIGJvdW5kYXJ5R2FwOiBbMCwgMC4wMV0sDQogICAgICAgICAgZGF0YTogWyLlvq7ljZoiLCAi5b6u5L+hIiwgIuS7iuaXpeWktOadoSIsICLnvZHmmJPmlrDpl7siLCAi6IW+6K6v5paw6Ze7Il0sDQogICAgICAgIH0sDQogICAgICAgIGdyaWQ6IHsNCiAgICAgICAgICBsZWZ0OiAiMyUiLA0KICAgICAgICAgIHJpZ2h0OiAiNCUiLA0KICAgICAgICAgIGJvdHRvbTogIjMlIiwNCiAgICAgICAgICBjb250YWluTGFiZWw6IHRydWUsDQogICAgICAgIH0sDQogICAgICAgIHNlcmllczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGRhdGE6IFsxMjAsIDIwMCwgMTUwLCA4MCwgNzAsIDExMCwgMTMwXSwNCiAgICAgICAgICAgIHR5cGU6ICJiYXIiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICB9Ow0KICAgICAgb3B0aW9uICYmIHRoaXMubXlDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsNCiAgICB9LA0KICAgIGFzeW5jIGRvd25sb2FkQnJpZUZpbmcoZmxhZykgew0KICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJhcHAvc2V0X0xvZGluZyIsIHRydWUpOw0KICAgICAgLyog5L2/55So5b6u5L+h5YWs5LyX5Y+35qih5p2/ICovDQogICAgICBpZiAodGhpcy5wcmVWaWV3RGF0YS5pc1dlY2hhdCkgew0KICAgICAgICBhd2FpdCBBUEkuZXhwb3J0V29ybGQoeyByZXBvcnRJZDogdGhpcy5wcmVWaWV3RGF0YS5icmllZmluZ0lkIH0pLnRoZW4oDQogICAgICAgICAgKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHRoaXMucHJlVmlld0RhdGEuaXNXZWNoYXQgPT0gMSkgew0KICAgICAgICAgICAgICB0aGlzLmRvd25Mb2FkWGxzKHJlcyk7DQogICAgICAgICAgICAgIHRoaXMudGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4i+i9veWujOaIkCIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgICAgICB0aGlzLnN3aXRjaFByZXZpZXcoKTsNCiAgICAgICAgICAgICAgfSwgMTAwMCk7DQogICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMucHJlVmlld0RhdGEuaXNXZWNoYXQgPT0gMikgew0KICAgICAgICAgICAgICBsZXQgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKTsNCiAgICAgICAgICAgICAgYS5ocmVmID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwocmVzKTsNCiAgICAgICAgICAgICAgYS5kb3dubG9hZCA9ICJicmllZmluZy5kb2N4IjsNCiAgICAgICAgICAgICAgYS5jbGljaygpOw0KICAgICAgICAgICAgICB0aGlzLnRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IG1lc3NhZ2U6ICLkuIvovb3lrozmiJAiLCB0eXBlOiAic3VjY2VzcyIgfSk7DQogICAgICAgICAgICAgICAgdGhpcy5zd2l0Y2hQcmV2aWV3KCk7DQogICAgICAgICAgICAgIH0sIDEwMDApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8qIOS9v+eUqOaZrumAmuaooeadvyAqLw0KICAgICAgICBsZXQgZG9tID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI2JyaWVGaW5nIik7DQoNCiAgICAgICAgaWYgKGRvbSkgew0KICAgICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgiYXBwL3NldF9Mb2RpbmciLCB0cnVlKTsNCiAgICAgICAgICB0aGlzLmdldFBkZihkb20pOw0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJhcHAvc2V0X0xvZGluZyIsIGZhbHNlKTsNCiAgICAgICAgICB9LCAyMDAwKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnRpbWVyID0gc2V0SW50ZXJ2YWwoKCkgPT4gew0KICAgICAgICAgICAgZG9tID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI2JyaWVGaW5nIik7DQogICAgICAgICAgICBpZiAoZG9tKSB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0UGRmKGRvbSk7DQogICAgICAgICAgICAgIHRoaXMuc3dpdGNoUHJldmlldygpOw0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuS4i+i9veaIkOWKnyIsIHR5cGU6ICJzdWNjZXNzIiB9KTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9LCAyMDAwKTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgUmVwb3J0U3RhdGlzaWNzKCkgew0KICAgICAgdGhpcy4kZW1pdCgiUmVwb3J0U3RhdGlzaWNzIik7DQogICAgfSwNCiAgICAvKiDojrflj5bor6bmg4XmlbDmja4gKi8NCiAgICBhc3luYyBnZXRJbmZvRGF0YSgpIHsNCiAgICAgIGxldCByZXM7DQogICAgICAvKiDlvZPliY3kuLrnlJ/miJDliY3pooTop4ggKi8NCiAgICAgIGlmICh0aGlzLnByZVZpZXdEYXRhLnByZXZpZXdEYXRhKSB7DQogICAgICAgIHRoaXMuY29udGVudCA9IEpTT04ucGFyc2UodGhpcy5wcmVWaWV3RGF0YS5wcmV2aWV3RGF0YSk7DQogICAgICAgIHRoaXMuaW5mb0RhdGEudGl0bGUgPSB0aGlzLmNvbnRlbnQudGl0bGU7DQogICAgICAgIHRoaXMudGFibGVEYXRhID0gdGhpcy5jb250ZW50LmhvdExpc3Q7DQogICAgICAgIHRoaXMuZGVzY3JpcHRpb24gPSB0aGlzLmNvbnRlbnQuZGVzY3JpcHRpb247DQogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgIHRyeSB7DQogICAgICAgICAgICB0aGlzLnJlbmRlckxpbmVDaGFydHMoKTsNCiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge30NCiAgICAgICAgfSwgMjAwKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgcmVzID0gYXdhaXQgQVBJLmJyaWVmaW5nSW5mbyh0aGlzLnByZVZpZXdEYXRhLmJyaWVmaW5nSWQpOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLmluZm9EYXRhID0gcmVzLmRhdGE7DQogICAgICAgIHRoaXMuY29udGVudCA9IEpTT04ucGFyc2UocmVzLmRhdGEuY29udGVudCk7DQogICAgICAgIGlmICh0aGlzLmNvbnRlbnQpIHsNCiAgICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHRoaXMuY29udGVudC5ob3RMaXN0Ow0KICAgICAgICAgIHRoaXMuZGVzY3JpcHRpb24gPSB0aGlzLmNvbnRlbnQuZGVzY3JpcHRpb247DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi5pWw5o2u6I635Y+W5aSx6LSlIiwgdHlwZTogImVycm9yIiB9KTsNCiAgICAgIH0NCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMucmVuZGVyTGluZUNoYXJ0cygpOw0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHt9DQogICAgfSwNCiAgICAvKiDmiZPlvIDlpJbnvZHpk77mjqUgKi8NCiAgICBvcGVuTmV3Vmlld1dlY2hhdChpdGVtKSB7DQogICAgICBpZiAoIWl0ZW0uc2hvcnRVcmwpIHJldHVybjsNCiAgICAgIGlmIChpdGVtLnNvdXJjZVR5cGUgPT0gMSkgew0KICAgICAgICB3aW5kb3cub3BlbihpdGVtLnNob3J0VXJsKTsNCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5zb3VyY2VUeXBlID09IDIpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oaXRlbS5vcmlnaW5hbFVybCk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiDlm77ooajmlbDmja7lpITnkIYgKi8NCiAgICBjaHJ0c0RhdGFIYW5kbGUoKSB7DQogICAgICBsZXQgV2VBcnIgPSBbXSwNCiAgICAgICAgV3lBcnIgPSBbXSwNCiAgICAgICAgWGRhdGEgPSBbXSwNCiAgICAgICAgZW5kX1RpbWUsDQogICAgICAgIHN0YXJ0X1RpbWUsDQogICAgICAgIFllYXIsDQogICAgICAgIGVuZFllYXI7DQogICAgICAvKiDlvZPliY3mmK/miqXlkYror6bmg4UgKi8NCiAgICAgIGlmICghdGhpcy5wcmVWaWV3RGF0YS5wcmV2aWV3RGF0YSAmJiB0aGlzLmNvbnRlbnQuZGF0ZVR5cGUpIHsNCiAgICAgICAgc3dpdGNoICh0aGlzLmNvbnRlbnQuZGF0ZVR5cGUpIHsNCiAgICAgICAgICBjYXNlICJob3VyIiAvKiDmjInlsI/ml7borqHnrpcgKi86DQogICAgICAgICAgICBXZUFyci5sZW5ndGggPSAyNDsNCiAgICAgICAgICAgIFd5QXJyLmxlbmd0aCA9IDI0Ow0KICAgICAgICAgICAgV2VBcnIuZmlsbCgwKTsNCiAgICAgICAgICAgIFd5QXJyLmZpbGwoMCk7DQogICAgICAgICAgICAvKiDlpITnkIZ46L205pWw5o2uICovDQogICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IDI0OyBpKyspIHsNCiAgICAgICAgICAgICAgWGRhdGEucHVzaChgJHtpLnRvU3RyaW5nKCkubGVuZ3RoID09IDEgPyAiMCIgKyBpIDogaX06MDBgKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8qIOWkhOeQhnnovbTmlbDmja4gKi8NCiAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMuY29udGVudC53eVRyZW5kQ291bnQpLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgbGV0IGtleSA9IGl0ZW0uc2xpY2UoMTEsIDEzKSArICI6MDAiOw0KICAgICAgICAgICAgICBXeUFycltYZGF0YS5sYXN0SW5kZXhPZihrZXkpXSA9IHRoaXMuY29udGVudC53eVRyZW5kQ291bnRbaXRlbV07DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMuY29udGVudC53eFRyZW5kQ291bnQpLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgbGV0IGtleSA9IGl0ZW0uc2xpY2UoMTEsIDEzKSArICI6MDAiOw0KICAgICAgICAgICAgICBXZUFycltYZGF0YS5sYXN0SW5kZXhPZihrZXkpXSA9IHRoaXMuY29udGVudC53eVRyZW5kQ291bnRbaXRlbV07DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgImRheSI6DQogICAgICAgICAgICAoZW5kX1RpbWUgPSBOdW1iZXIodGhpcy5jb250ZW50LmVuZFRpbWUuc2xpY2UoOCwgMTApKSksDQogICAgICAgICAgICAgIChzdGFydF9UaW1lID0gTnVtYmVyKHRoaXMuY29udGVudC5zdGFydFRpbWUuc2xpY2UoOCwgMTApKSksDQogICAgICAgICAgICAgIChZZWFyID0gdGhpcy5jb250ZW50LnN0YXJ0VGltZS5zbGljZSgwLCA3KSk7DQogICAgICAgICAgICBlbmRZZWFyID0gdGhpcy5jb250ZW50LmVuZFRpbWUuc2xpY2UoMCwgNyk7DQogICAgICAgICAgICAvKiDot6jotorkuKTkuKrmnIjnmoTmg4XlhrUgKi8NCiAgICAgICAgICAgIGxldCBlbmQgPSBOdW1iZXIodGhpcy5jb250ZW50LmVuZFRpbWUuc2xpY2UoNSwgNykpLA0KICAgICAgICAgICAgICBzdGFydCA9IE51bWJlcih0aGlzLmNvbnRlbnQuc3RhcnRUaW1lLnNsaWNlKDUsIDcpKSwNCiAgICAgICAgICAgICAgbnVtID0gMzAgLSBOdW1iZXIoc3RhcnRfVGltZSk7DQogICAgICAgICAgICBpZiAoZW5kID4gc3RhcnQpIHsNCiAgICAgICAgICAgICAgZW5kX1RpbWUgPSBlbmRfVGltZSArIG51bTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIFdlQXJyLmxlbmd0aCA9IGVuZF9UaW1lOw0KICAgICAgICAgICAgV3lBcnIubGVuZ3RoID0gZW5kX1RpbWU7DQogICAgICAgICAgICAvKiDmlbDmja7loavlhYUgKi8NCiAgICAgICAgICAgIFdlQXJyLmZpbGwoMCk7DQogICAgICAgICAgICBXeUFyci5maWxsKDApOw0KICAgICAgICAgICAgLyog5b6q546v5pWw5o2uICovDQogICAgICAgICAgICBsZXQgYSwNCiAgICAgICAgICAgICAgaSA9IDE7DQogICAgICAgICAgICB3aGlsZSAoWGRhdGEubGVuZ3RoIDwgZW5kX1RpbWUpIHsNCiAgICAgICAgICAgICAgYSA9IGk7DQogICAgICAgICAgICAgIGxldCBpdGVtOw0KICAgICAgICAgICAgICBpZiAoc3RhcnRfVGltZSA8PSAzMCkgew0KICAgICAgICAgICAgICAgIGkgPSBzdGFydF9UaW1lOw0KICAgICAgICAgICAgICB9IGVsc2UgaWYgKHN0YXJ0X1RpbWUgPD0gMzEpIHsNCiAgICAgICAgICAgICAgICBpID0gMTsNCiAgICAgICAgICAgICAgICBhID0gMTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBpZiAoc3RhcnRfVGltZSA+IDMwKSB7DQogICAgICAgICAgICAgICAgaXRlbSA9IGVuZFllYXIgKyAiLSIgKyAoaS50b1N0cmluZygpLmxlbmd0aCA9PSAxID8gIjAiICsgaSA6IGkpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIGl0ZW0gPSBZZWFyICsgIi0iICsgKGkudG9TdHJpbmcoKS5sZW5ndGggPT0gMSA/ICIwIiArIGkgOiBpKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBYZGF0YS5wdXNoKGl0ZW0pOw0KICAgICAgICAgICAgICBpID0gYTsNCiAgICAgICAgICAgICAgKytzdGFydF9UaW1lOw0KICAgICAgICAgICAgICArK2k7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAvKiDlpITnkIZ56L205pWw5o2uICovDQogICAgICAgICAgICBPYmplY3Qua2V5cyh0aGlzLmNvbnRlbnQud3lUcmVuZENvdW50KS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGxldCBrZXkgPSBpdGVtLnNsaWNlKDgsIDExKTsNCiAgICAgICAgICAgICAgV3lBcnJbWGRhdGEubGFzdEluZGV4T2YoWWVhciArICItIiArIGtleSldID0NCiAgICAgICAgICAgICAgICB0aGlzLmNvbnRlbnQud3lUcmVuZENvdW50W2l0ZW1dOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICBPYmplY3Qua2V5cyh0aGlzLmNvbnRlbnQud3hUcmVuZENvdW50KS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGxldCBrZXkgPSBpdGVtLnNsaWNlKDgsIDExKTsNCiAgICAgICAgICAgICAgV2VBcnJbWGRhdGEubGFzdEluZGV4T2YoWWVhciArICItIiArIGtleSldID0NCiAgICAgICAgICAgICAgICB0aGlzLmNvbnRlbnQud3lUcmVuZENvdW50W2l0ZW1dOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICJtb250aCI6DQogICAgICAgICAgICBlbmRfVGltZSA9IE51bWJlcih0aGlzLmNvbnRlbnQuZW5kVGltZS5zbGljZSg1LCA3KSk7DQogICAgICAgICAgICBzdGFydF9UaW1lID0gTnVtYmVyKHRoaXMuY29udGVudC5zdGFydFRpbWUuc2xpY2UoNSwgNykpOw0KICAgICAgICAgICAgWWVhciA9IHRoaXMuY29udGVudC5zdGFydFRpbWUuc2xpY2UoMCwgNCk7DQogICAgICAgICAgICBXZUFyci5sZW5ndGggPSBlbmRfVGltZTsNCiAgICAgICAgICAgIFd5QXJyLmxlbmd0aCA9IGVuZF9UaW1lOw0KICAgICAgICAgICAgV2VBcnIuZmlsbCgwKTsNCiAgICAgICAgICAgIFd5QXJyLmZpbGwoMCk7DQogICAgICAgICAgICBmb3IgKGxldCBpID0gc3RhcnRfVGltZTsgaSA8PSBlbmRfVGltZTsgaSsrKSB7DQogICAgICAgICAgICAgIGxldCBpdGVtID0gWWVhciArICItIiArIChpLnRvU3RyaW5nKCkubGVuZ3RoID09IDEgPyAiMCIgKyBpIDogaSk7DQogICAgICAgICAgICAgIFhkYXRhLnB1c2goaXRlbSk7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8qIOWkhOeQhnnovbTmlbDmja4gKi8NCiAgICAgICAgICAgIE9iamVjdC5rZXlzKHRoaXMuY29udGVudC53eVRyZW5kQ291bnQpLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgbGV0IGtleSA9IGl0ZW0uc2xpY2UoNSwgNyk7DQogICAgICAgICAgICAgIFd5QXJyW1hkYXRhLmxhc3RJbmRleE9mKFllYXIgKyAiLSIgKyBrZXkpXSA9DQogICAgICAgICAgICAgICAgdGhpcy5jb250ZW50Lnd5VHJlbmRDb3VudFtpdGVtXTsNCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICBPYmplY3Qua2V5cyh0aGlzLmNvbnRlbnQud3hUcmVuZENvdW50KS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGxldCBrZXkgPSBpdGVtLnNsaWNlKDUsIDcpOw0KICAgICAgICAgICAgICBsZXQgaW5kZXggPSBYZGF0YS5sYXN0SW5kZXhPZihZZWFyICsgIi0iICsga2V5KTsNCiAgICAgICAgICAgICAgV2VBcnJbaW5kZXhdID0gdGhpcy5jb250ZW50Lnd4VHJlbmRDb3VudFtpdGVtXTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICAgIHRoaXMueEF4aXMgPSB7DQogICAgICAgICAgdHlwZTogImNhdGVnb3J5IiwNCiAgICAgICAgICBkYXRhOiBYZGF0YSwNCiAgICAgICAgfTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8qIOW9k+WJjeaYr+iNieeov+ivpuaDhSAqLw0KDQogICAgICAgIGxldCBXeCA9IHRoaXMuY29udGVudC53eFRyZW5kQ291bnQsDQogICAgICAgICAgV0VCID0gdGhpcy5jb250ZW50Lnd5VHJlbmRDb3VudDsNCg0KICAgICAgICBPYmplY3Qua2V5cyhXeCkuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgIFdlQXJyLnB1c2goV3hbaXRlbV0pOw0KICAgICAgICAgIFhkYXRhLnB1c2goaXRlbSk7DQogICAgICAgIH0pOw0KICAgICAgICBPYmplY3Qua2V5cyhXRUIpLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBXeUFyci5wdXNoKFdFQltpdGVtXSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLnhBeGlzID0gew0KICAgICAgICAgIHR5cGU6ICJjYXRlZ29yeSIsDQogICAgICAgICAgZGF0YTogWGRhdGEsDQogICAgICAgIH07DQogICAgICB9DQogICAgICByZXR1cm4gWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIuW+ruS/oSIsDQogICAgICAgICAgZGF0YTogV2VBcnIsDQogICAgICAgICAgdHlwZTogImxpbmUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogIue9keermSIsDQogICAgICAgICAgZGF0YTogV3lBcnIsDQogICAgICAgICAgdHlwZTogImxpbmUiLA0KICAgICAgICB9LA0KICAgICAgXTsNCiAgICB9LA0KICAgIC8qIOaWh+S7tua1geino+eggSAqLw0KICAgIGRvd25Mb2FkWGxzKHJlcykgew0KICAgICAgbGV0IGZpbGVOYW1lID0gdGhpcy5wcmVWaWV3RGF0YS5yZXBvcnROYW1lOw0KICAgICAgaWYgKCJkb3dubG9hZCIgaW4gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpKSB7DQogICAgICAgIGNvbnN0IGEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJhIik7IC8v5Yib5bu65LiA5LiqYeagh+etvg0KICAgICAgICBhLmRvd25sb2FkID0gZmlsZU5hbWUgKyAiLmRvY3giOyAvL+aMh+WumuaWh+S7tuWQjeensA0KICAgICAgICBhLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7IC8v6aG16Z2i6ZqQ6JePDQogICAgICAgIGEuaHJlZiA9IFVSTC5jcmVhdGVPYmplY3RVUkwocmVzKTsgLy8gaHJlZueUqOS6juS4i+i9veWcsOWdgA0KICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpOyAvL+aPkuWIsOmhtemdouS4ig0KICAgICAgICBhLmNsaWNrKCk7IC8v6YCa6L+H54K55Ye76Kem5Y+RDQogICAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoYS5ocmVmKTsgLy/ph4rmlL5VUkwg5a+56LGhDQogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYSk7IC8v5Yig5o6JYeagh+etvg0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy9JRTEwICsg5LiL6L29DQogICAgICAgIG5hdmlnYXRvci5tc1NhdmVCbG9iKHJlcywgZmlsZU5hbWUpOw0KICAgICAgfQ0KICAgIH0sDQogICAgc3dpdGNoUHJldmlldygpIHsNCiAgICAgIHRoaXMuJGVtaXQoInN3aXRjaFByZXZpZXciKTsNCiAgICB9LA0KICAgIC8qIOaWh+eroOe7n+iuoeWIl+ihqCAqLw0KICAgIGFzeW5jIFN0YXRpc3RpY2FsTGlzdCgpIHsNCiAgICAgIHRoaXMuZGlhbG9nVGFibGVWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBBUEkuc3RhdGlzdGljcyh7DQogICAgICAgIHBhZ2VTaXplOiA5OSwNCiAgICAgICAgcGFnZU51bTogdGhpcy5wYWdlTnVtMSwNCiAgICAgICAgcmVwb3J0SWQ6IHRoaXMucHJlVmlld0RhdGEuYnJpZWZpbmdJZCwNCiAgICAgIH0pOw0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLndlY2hhdExpc3QgPSByZXMucm93czsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qIOaJk+W8gOWklue9kemTvuaOpSAqLw0KICAgIG9wZW5OZXdWaWV3KGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLnNvdXJjZVR5cGUgPT0gMSkgew0KICAgICAgICBpZiAoaXRlbS5zaG9ydFVybCkgew0KICAgICAgICAgIHdpbmRvdy5vcGVuKGl0ZW0uc2hvcnRVcmwpOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsgbWVzc2FnZTogIuivpeaWh+eroOayoeacieWOn+aWh+mTvuaOpSIgfSk7DQogICAgICB9IGVsc2UgaWYgKGl0ZW0uc291cmNlVHlwZSA9PSAyKSB7DQogICAgICAgIGlmIChpdGVtLm9yaWdpbmFsVXJsKSB7DQogICAgICAgICAgd2luZG93Lm9wZW4oaXRlbS5vcmlnaW5hbFVybCk7DQogICAgICAgICAgcmV0dXJuOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuJG1lc3NhZ2UoeyBtZXNzYWdlOiAi6K+l5paH56ug5rKh5pyJ5Y6f5paH6ZO+5o6lIiB9KTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lcik7DQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["briefingPreview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "briefingPreview.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<!-- 简报预览 -->\r\n<template>\r\n  <div>\r\n    <div class=\"PreviewMain\" id=\"brieFing\">\r\n      <div class=\"topTool\">\r\n        <div class=\"callback\" @click=\"switchPreview\">\r\n          <i class=\"el-icon-arrow-left iconCallback\"></i>返回\r\n        </div>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          @click=\"ReportStatisics\"\r\n          ref=\"down\"\r\n          v-if=\"preViewData.reportStatus == 1\"\r\n          >生成报告</el-button\r\n        >\r\n        <el-button\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          class=\"download\"\r\n          @click=\"downloadBrieFing('download')\"\r\n          ref=\"down\"\r\n          v-if=\"!preViewData.preview\"\r\n          >下载报告</el-button\r\n        >\r\n        {{ preViewData.preView }}\r\n      </div>\r\n      <div class=\"title\">\r\n        <h1>{{ infoData.title }}</h1>\r\n        <h5>{{ content ? content.detectionTime : null }}</h5>\r\n      </div>\r\n      <!-- <div class=\"describe\">\r\n        <div class=\"BoxHeader\">\r\n          01 报告描述\r\n        </div>\r\n        <div class=\"cellStyle\">\r\n          <ul>\r\n            <li v-for=\"(item, key) in content.description\" :key=\"key\">\r\n              {{ item }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n                </div>  2023 9-4   沈老师  暂时注释-->\r\n      <div class=\"detail-container\">\r\n        <template v-if=\"!preViewData.isWechat\">\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">01 事件走势</div>\r\n            <span\r\n              style=\"position: relative; top: 20px; left: 55px; font-size: 14px\"\r\n              v-if=\"content\"\r\n              >{{ content ? content.eventDesc : null }}</span\r\n            >\r\n            <div class=\"charts\" id=\"line\"></div>\r\n          </div>\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">02 热门文章</div>\r\n            <div class=\"tableStyle\">\r\n              <div>\r\n                <el-table\r\n                  size=\"mini\"\r\n                  :data=\"tableData\"\r\n                  border\r\n                  style=\"width: 92.5%\"\r\n                  :header-cell-style=\"{\r\n                    textAlign: 'center',\r\n                    backgroundColor: 'rgb(64, 158, 255)',\r\n                    color: '#ffff',\r\n                  }\"\r\n                  :cell-style=\"{}\"\r\n                >\r\n                  <el-table-column\r\n                    type=\"index\"\r\n                    label=\"序号\"\r\n                    width=\"60\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"cnTitle\"\r\n                    label=\"标题\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span @click=\"openNewView(scope.row)\" class=\"tableTitle\">\r\n                        {{ scope.row.cnTitle }}\r\n                      </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishType\"\r\n                    label=\"平台类型\"\r\n                    width=\"130\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{\r\n                        scope.row.sourceType == 1\r\n                          ? \"微信公众号\"\r\n                          : null || scope.row.sourceType == 2\r\n                          ? \"网站\"\r\n                          : null || scope.row.sourceType == 3\r\n                          ? \"手动录入\"\r\n                          : null\r\n                      }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"sourceName\"\r\n                    label=\"媒体来源\"\r\n                    width=\"300\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishTime\"\r\n                    label=\"发布时间\"\r\n                    width=\"180\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{ formatDate(scope.row.publishTime) }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"preViewData.isWechat == 1\">\r\n          <div class=\"title\" style=\"font-weight: 600; font-size: 20px\">\r\n            {{ preViewData.reportName }}报送条目\r\n          </div>\r\n          <!-- <div class=\"title\" style=\"font-weight: 100;font-size: 32px;margin: 20px auto;width: 50%;font-family: cursive;\">\r\n          （★条目为“境外媒体关于我国的文章”，已由国内翻译、转载，并审核溯源可靠、内容准确。）</div> -->\r\n          <div\r\n            v-for=\"(item, index) in wechatList\"\r\n            :key=\"index\"\r\n            class=\"worldStyle\"\r\n          >\r\n            <p class=\"cnSummary\">\r\n              <!-- {{ index + 1 + \".微信公众号\" + item.sourceName }} -->\r\n              {{ index + 1 + \".\" + item.sourceName }}\r\n              {{\r\n                item.publishTime\r\n                  ? new Date(item.publishTime).getMonth() +\r\n                    1 +\r\n                    \"月\" +\r\n                    new Date(item.publishTime).getDate() +\r\n                    \"日\"\r\n                  : \"\"\r\n              }}\r\n              {{ \"报道:\" + (item.cnSummary ? item.cnSummary : \"暂无摘要\") }}\r\n            </p>\r\n            <p class=\"link\" @click=\"openNewViewWechat(item)\">\r\n              {{ \"(\" + (item.shortUrl ? item.shortUrl : \"暂无链接\") + \")\" }}\r\n            </p>\r\n          </div>\r\n        </template>\r\n        <div class=\"docx-container\">\r\n          <div ref=\"file\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { renderLineCharts, renderAnnular } from \"@/utils/renderLine.js\";\r\nimport { antiShake } from \"@/utils/utils.js\";\r\nimport { formatDate } from \"@/utils/index.js\";\r\nimport * as echarts from \"echarts\";\r\nimport API from \"@/api/ScienceApi/briefing.js\";\r\n// 引入docx-preview插件\r\nlet docx = require(\"docx-preview\");\r\nexport default {\r\n  props: {\r\n    preViewData: {\r\n      required: true,\r\n      type: Object,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      downloading: false,\r\n      myChart: null,\r\n      infoData: {},\r\n      content: {\r\n        detectionTime: \"\",\r\n      },\r\n      xAxis: {},\r\n      timer: null,\r\n      wechatList: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.formatDate = formatDate;\r\n  },\r\n  created() {\r\n    if (this.preViewData.flag) {\r\n      this.downloadBrieFing();\r\n    }\r\n\r\n    /* 微信公众号模板 */\r\n    if (this.preViewData.isWechat == 1) {\r\n      this.StatisticalList();\r\n    } else if (this.preViewData.isWechat == 2) {\r\n      this.getWorld();\r\n    } else {\r\n      this.getInfoData();\r\n    }\r\n  },\r\n  methods: {\r\n    renderLineCharts() {\r\n      let data = this.chrtsDataHandle();\r\n      new renderLineCharts({\r\n        dom: \"line\",\r\n        data: data,\r\n        titleShow: false,\r\n        xAxis: this.xAxis,\r\n      }).render();\r\n      // this.renderAnnular()\r\n    },\r\n    async getWorld() {\r\n      await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n        (res) => {\r\n          docx.renderAsync(res, this.$refs.file); // 渲染到页面\r\n          this.$store.commit(\"app/set_Loding\", false);\r\n        }\r\n      );\r\n    },\r\n    renderBar() {\r\n      var chartDom = document.getElementById(\"bar\");\r\n      this.myChart = echarts.init(chartDom);\r\n      var option;\r\n      option = {\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 250,\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"shadow\",\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          boundaryGap: [0, 0.01],\r\n          data: [\"微博\", \"微信\", \"今日头条\", \"网易新闻\", \"腾讯新闻\"],\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true,\r\n        },\r\n        series: [\r\n          {\r\n            data: [120, 200, 150, 80, 70, 110, 130],\r\n            type: \"bar\",\r\n          },\r\n        ],\r\n      };\r\n      option && this.myChart.setOption(option);\r\n    },\r\n    async downloadBrieFing(flag) {\r\n      this.$store.commit(\"app/set_Loding\", true);\r\n      /* 使用微信公众号模板 */\r\n      if (this.preViewData.isWechat) {\r\n        await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n          (res) => {\r\n            if (this.preViewData.isWechat == 1) {\r\n              this.downLoadXls(res);\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            } else if (this.preViewData.isWechat == 2) {\r\n              let a = document.createElement(\"a\");\r\n              a.href = window.URL.createObjectURL(res);\r\n              a.download = \"briefing.docx\";\r\n              a.click();\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            }\r\n          }\r\n        );\r\n      } else {\r\n        /* 使用普通模板 */\r\n        let dom = document.querySelector(\"#brieFing\");\r\n\r\n        if (dom) {\r\n          this.$store.commit(\"app/set_Loding\", true);\r\n          this.getPdf(dom);\r\n          setTimeout(() => {\r\n            this.$store.commit(\"app/set_Loding\", false);\r\n          }, 2000);\r\n        } else {\r\n          this.timer = setInterval(() => {\r\n            dom = document.querySelector(\"#brieFing\");\r\n            if (dom) {\r\n              this.getPdf(dom);\r\n              this.switchPreview();\r\n              this.$message({ message: \"下载成功\", type: \"success\" });\r\n            }\r\n          }, 2000);\r\n        }\r\n      }\r\n    },\r\n    ReportStatisics() {\r\n      this.$emit(\"ReportStatisics\");\r\n    },\r\n    /* 获取详情数据 */\r\n    async getInfoData() {\r\n      let res;\r\n      /* 当前为生成前预览 */\r\n      if (this.preViewData.previewData) {\r\n        this.content = JSON.parse(this.preViewData.previewData);\r\n        this.infoData.title = this.content.title;\r\n        this.tableData = this.content.hotList;\r\n        this.description = this.content.description;\r\n        setTimeout(() => {\r\n          try {\r\n            this.renderLineCharts();\r\n          } catch (error) {}\r\n        }, 200);\r\n        return;\r\n      }\r\n      res = await API.briefingInfo(this.preViewData.briefingId);\r\n      if (res.code == 200) {\r\n        this.infoData = res.data;\r\n        this.content = JSON.parse(res.data.content);\r\n        if (this.content) {\r\n          this.tableData = this.content.hotList;\r\n          this.description = this.content.description;\r\n        }\r\n      } else {\r\n        this.$message({ message: \"数据获取失败\", type: \"error\" });\r\n      }\r\n      try {\r\n        this.renderLineCharts();\r\n      } catch (error) {}\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewViewWechat(item) {\r\n      if (!item.shortUrl) return;\r\n      if (item.sourceType == 1) {\r\n        window.open(item.shortUrl);\r\n      } else if (item.sourceType == 2) {\r\n        window.open(item.originalUrl);\r\n      }\r\n    },\r\n    /* 图表数据处理 */\r\n    chrtsDataHandle() {\r\n      let WeArr = [],\r\n        WyArr = [],\r\n        Xdata = [],\r\n        end_Time,\r\n        start_Time,\r\n        Year,\r\n        endYear;\r\n      /* 当前是报告详情 */\r\n      if (!this.preViewData.previewData && this.content.dateType) {\r\n        switch (this.content.dateType) {\r\n          case \"hour\" /* 按小时计算 */:\r\n            WeArr.length = 24;\r\n            WyArr.length = 24;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 处理x轴数据 */\r\n            for (let i = 0; i < 24; i++) {\r\n              Xdata.push(`${i.toString().length == 1 ? \"0\" + i : i}:00`);\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WyArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WeArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"day\":\r\n            (end_Time = Number(this.content.endTime.slice(8, 10))),\r\n              (start_Time = Number(this.content.startTime.slice(8, 10))),\r\n              (Year = this.content.startTime.slice(0, 7));\r\n            endYear = this.content.endTime.slice(0, 7);\r\n            /* 跨越两个月的情况 */\r\n            let end = Number(this.content.endTime.slice(5, 7)),\r\n              start = Number(this.content.startTime.slice(5, 7)),\r\n              num = 30 - Number(start_Time);\r\n            if (end > start) {\r\n              end_Time = end_Time + num;\r\n            }\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            /* 数据填充 */\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 循环数据 */\r\n            let a,\r\n              i = 1;\r\n            while (Xdata.length < end_Time) {\r\n              a = i;\r\n              let item;\r\n              if (start_Time <= 30) {\r\n                i = start_Time;\r\n              } else if (start_Time <= 31) {\r\n                i = 1;\r\n                a = 1;\r\n              }\r\n              if (start_Time > 30) {\r\n                item = endYear + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              } else {\r\n                item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              }\r\n              Xdata.push(item);\r\n              i = a;\r\n              ++start_Time;\r\n              ++i;\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WeArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"month\":\r\n            end_Time = Number(this.content.endTime.slice(5, 7));\r\n            start_Time = Number(this.content.startTime.slice(5, 7));\r\n            Year = this.content.startTime.slice(0, 4);\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            for (let i = start_Time; i <= end_Time; i++) {\r\n              let item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              Xdata.push(item);\r\n            }\r\n\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              let index = Xdata.lastIndexOf(Year + \"-\" + key);\r\n              WeArr[index] = this.content.wxTrendCount[item];\r\n            });\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      } else {\r\n        /* 当前是草稿详情 */\r\n\r\n        let Wx = this.content.wxTrendCount,\r\n          WEB = this.content.wyTrendCount;\r\n\r\n        Object.keys(Wx).forEach((item) => {\r\n          WeArr.push(Wx[item]);\r\n          Xdata.push(item);\r\n        });\r\n        Object.keys(WEB).forEach((item) => {\r\n          WyArr.push(WEB[item]);\r\n        });\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      }\r\n      return [\r\n        {\r\n          name: \"微信\",\r\n          data: WeArr,\r\n          type: \"line\",\r\n        },\r\n        {\r\n          name: \"网站\",\r\n          data: WyArr,\r\n          type: \"line\",\r\n        },\r\n      ];\r\n    },\r\n    /* 文件流解码 */\r\n    downLoadXls(res) {\r\n      let fileName = this.preViewData.reportName;\r\n      if (\"download\" in document.createElement(\"a\")) {\r\n        const a = document.createElement(\"a\"); //创建一个a标签\r\n        a.download = fileName + \".docx\"; //指定文件名称\r\n        a.style.display = \"none\"; //页面隐藏\r\n        a.href = URL.createObjectURL(res); // href用于下载地址\r\n        document.body.appendChild(a); //插到页面上\r\n        a.click(); //通过点击触发\r\n        URL.revokeObjectURL(a.href); //释放URL 对象\r\n        document.body.removeChild(a); //删掉a标签\r\n      } else {\r\n        //IE10 + 下载\r\n        navigator.msSaveBlob(res, fileName);\r\n      }\r\n    },\r\n    switchPreview() {\r\n      this.$emit(\"switchPreview\");\r\n    },\r\n    /* 文章统计列表 */\r\n    async StatisticalList() {\r\n      this.dialogTableVisible = true;\r\n      let res = await API.statistics({\r\n        pageSize: 99,\r\n        pageNum: this.pageNum1,\r\n        reportId: this.preViewData.briefingId,\r\n      });\r\n      if (res.code == 200) {\r\n        this.wechatList = res.rows;\r\n      }\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewView(item) {\r\n      if (item.sourceType == 1) {\r\n        if (item.shortUrl) {\r\n          window.open(item.shortUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      } else if (item.sourceType == 2) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      }\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tableTitle:hover {\r\n  color: #228fd3;\r\n  border-bottom: solid 1px #228fd3;\r\n}\r\n\r\n.PreviewMain {\r\n  .topTool {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    margin-bottom: 10px;\r\n\r\n    .callback {\r\n      font-size: 16px;\r\n      color: rgb(8, 166, 240);\r\n      cursor: pointer;\r\n    }\r\n\r\n    .iconCallback {\r\n      font-size: 18px;\r\n      color: rgb(8, 166, 240);\r\n    }\r\n  }\r\n\r\n  width: 90%;\r\n  overflow: hidden;\r\n  height: calc(100vh - 100px);\r\n  margin: 0 auto;\r\n  padding-top: 20px;\r\n  border: solid 1px #efefef;\r\n  box-shadow: 0px 2px 11px 9px #efefef;\r\n  margin-top: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .title {\r\n    text-align: center;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n\r\n    h1 {\r\n      font-size: 30px;\r\n    }\r\n  }\r\n\r\n  .describe {\r\n    width: 98%;\r\n    min-height: 200px;\r\n    margin: 15px auto;\r\n    box-shadow: 4px 6px 4px 2px #efefef;\r\n\r\n    .BoxHeader {\r\n      height: 40px;\r\n      line-height: 40px;\r\n      padding-left: 15px;\r\n      width: 100%;\r\n      border-top: solid 1px #e0dfdf;\r\n      border-left: solid 1px #e0dfdf;\r\n      border-right: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .cellStyle {\r\n      width: 100%;\r\n      height: 160px;\r\n      border: solid 1px #e0dfdf;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      p {\r\n        width: 90%;\r\n        font-size: 14px;\r\n        margin: 0 auto;\r\n      }\r\n\r\n      ul {\r\n        font-size: 14px;\r\n        line-height: 25px;\r\n\r\n        li {\r\n          list-style: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin-left: 15px;\r\n    }\r\n\r\n    .charts {\r\n      width: 100%;\r\n      height: 300px;\r\n      border: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      min-height: 400px;\r\n      border: solid 1px #e0dfdf;\r\n\r\n      div {\r\n        margin: 20px auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.world {\r\n  width: 80%;\r\n  margin: 0 auto;\r\n  min-height: 1200px;\r\n  background-color: #228fd3;\r\n}\r\n\r\n.worldStyle {\r\n  width: 50%;\r\n  margin: 0 auto;\r\n  text-overflow: clip;\r\n\r\n  .cnSummary {\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n    text-indent: 2em;\r\n  }\r\n\r\n  .link {\r\n    margin-top: -20px;\r\n    font-weight: 400;\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n  }\r\n\r\n  .link:hover {\r\n    color: #228fd3;\r\n    border-bottom: solid #228fd3 1px;\r\n  }\r\n}\r\n\r\n.detail-container {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n<style scoped>\r\n.docx-container ::v-deep .docx-wrapper {\r\n  background-color: #fff;\r\n  padding: 20px 20px;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx {\r\n  width: 55vw !important;\r\n  padding: 0rem !important;\r\n  min-height: auto !important;\r\n  box-shadow: none;\r\n  margin-bottom: 0;\r\n  line-height: 50px;\r\n  overflow-y: scroll;\r\n  height: 100vh;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}