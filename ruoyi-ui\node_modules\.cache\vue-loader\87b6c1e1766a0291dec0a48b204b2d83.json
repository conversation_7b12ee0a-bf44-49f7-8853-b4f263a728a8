{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue?vue&type=style&index=0&id=499712a1&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue", "mtime": 1753690547255}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi50YWJsZVRpdGxlOmhvdmVyIHsNCiAgY29sb3I6ICMyMjhmZDM7DQogIGJvcmRlci1ib3R0b206IHNvbGlkIDFweCAjMjI4ZmQzOw0KfQ0KDQouUHJldmlld01haW4gew0KICAudG9wVG9vbCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgcGFkZGluZzogMCAyMHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQoNCiAgICAuY2FsbGJhY2sgew0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgY29sb3I6IHJnYig4LCAxNjYsIDI0MCk7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgfQ0KDQogICAgLmljb25DYWxsYmFjayB7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBjb2xvcjogcmdiKDgsIDE2NiwgMjQwKTsNCiAgICB9DQogIH0NCg0KICB3aWR0aDogOTAlOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAxMDBweCk7DQogIG1hcmdpbjogMCBhdXRvOw0KICBwYWRkaW5nLXRvcDogMjBweDsNCiAgYm9yZGVyOiBzb2xpZCAxcHggI2VmZWZlZjsNCiAgYm94LXNoYWRvdzogMHB4IDJweCAxMXB4IDlweCAjZWZlZmVmOw0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KDQogIC50aXRsZSB7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KDQogICAgaDEgew0KICAgICAgZm9udC1zaXplOiAzMHB4Ow0KICAgIH0NCiAgfQ0KDQogIC5kZXNjcmliZSB7DQogICAgd2lkdGg6IDk4JTsNCiAgICBtaW4taGVpZ2h0OiAyMDBweDsNCiAgICBtYXJnaW46IDE1cHggYXV0bzsNCiAgICBib3gtc2hhZG93OiA0cHggNnB4IDRweCAycHggI2VmZWZlZjsNCg0KICAgIC5Cb3hIZWFkZXIgew0KICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDE1cHg7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGJvcmRlci10b3A6IHNvbGlkIDFweCAjZTBkZmRmOw0KICAgICAgYm9yZGVyLWxlZnQ6IHNvbGlkIDFweCAjZTBkZmRmOw0KICAgICAgYm9yZGVyLXJpZ2h0OiBzb2xpZCAxcHggI2UwZGZkZjsNCiAgICB9DQoNCiAgICAuY2VsbFN0eWxlIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiAxNjBweDsNCiAgICAgIGJvcmRlcjogc29saWQgMXB4ICNlMGRmZGY7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICBwIHsNCiAgICAgICAgd2lkdGg6IDkwJTsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAgIH0NCg0KICAgICAgdWwgew0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAyNXB4Ow0KDQogICAgICAgIGxpIHsNCiAgICAgICAgICBsaXN0LXN0eWxlOiBub25lOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLmluZm8gew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgbWFyZ2luLWxlZnQ6IDE1cHg7DQogICAgfQ0KDQogICAgLmNoYXJ0cyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMzAwcHg7DQogICAgICBib3JkZXI6IHNvbGlkIDFweCAjZTBkZmRmOw0KICAgIH0NCg0KICAgIC50YWJsZVN0eWxlIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgbWluLWhlaWdodDogNDAwcHg7DQogICAgICBib3JkZXI6IHNvbGlkIDFweCAjZTBkZmRmOw0KDQogICAgICBkaXYgew0KICAgICAgICBtYXJnaW46IDIwcHggYXV0bzsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLndvcmxkIHsNCiAgd2lkdGg6IDgwJTsNCiAgbWFyZ2luOiAwIGF1dG87DQogIG1pbi1oZWlnaHQ6IDEyMDBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzIyOGZkMzsNCn0NCg0KLndvcmxkU3R5bGUgew0KICB3aWR0aDogNTAlOw0KICBtYXJnaW46IDAgYXV0bzsNCiAgdGV4dC1vdmVyZmxvdzogY2xpcDsNCg0KICAuY25TdW1tYXJ5IHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgbGluZS1oZWlnaHQ6IDEuOGVtOw0KICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZyBTQywgc3lzdGVtLXVpLCAtYXBwbGUtc3lzdGVtLCBCbGlua01hY1N5c3RlbUZvbnQsDQogICAgICBIZWx2ZXRpY2EgTmV1ZSwgSGlyYWdpbm8gU2FucyBHQiwgTWljcm9zb2Z0IFlhSGVpIFVJLCBNaWNyb3NvZnQgWWFIZWksDQogICAgICBBcmlhbCwgc2Fucy1zZXJpZjsNCiAgICB0ZXh0LWluZGVudDogMmVtOw0KICB9DQoNCiAgLmxpbmsgew0KICAgIG1hcmdpbi10b3A6IC0yMHB4Ow0KICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAxLjhlbTsNCiAgICBmb250LWZhbWlseTogUGluZ0ZhbmcgU0MsIHN5c3RlbS11aSwgLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LA0KICAgICAgSGVsdmV0aWNhIE5ldWUsIEhpcmFnaW5vIFNhbnMgR0IsIE1pY3Jvc29mdCBZYUhlaSBVSSwgTWljcm9zb2Z0IFlhSGVpLA0KICAgICAgQXJpYWwsIHNhbnMtc2VyaWY7DQogIH0NCg0KICAubGluazpob3ZlciB7DQogICAgY29sb3I6ICMyMjhmZDM7DQogICAgYm9yZGVyLWJvdHRvbTogc29saWQgIzIyOGZkMyAxcHg7DQogIH0NCn0NCg0KLmRldGFpbC1jb250YWluZXIgew0KICBmbGV4OiAxOw0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["briefingPreview.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2iBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "briefingPreview.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<!-- 简报预览 -->\r\n<template>\r\n  <div>\r\n    <div class=\"PreviewMain\" id=\"brieFing\">\r\n      <div class=\"topTool\">\r\n        <div class=\"callback\" @click=\"switchPreview\">\r\n          <i class=\"el-icon-arrow-left iconCallback\"></i>返回\r\n        </div>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          @click=\"ReportStatisics\"\r\n          ref=\"down\"\r\n          v-if=\"preViewData.reportStatus == 1\"\r\n          >生成报告</el-button\r\n        >\r\n        <el-button\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          class=\"download\"\r\n          @click=\"downloadBrieFing('download')\"\r\n          ref=\"down\"\r\n          v-if=\"!preViewData.preview\"\r\n          >下载报告</el-button\r\n        >\r\n        {{ preViewData.preView }}\r\n      </div>\r\n      <div class=\"title\">\r\n        <h1>{{ infoData.title }}</h1>\r\n        <h5>{{ content ? content.detectionTime : null }}</h5>\r\n      </div>\r\n      <!-- <div class=\"describe\">\r\n        <div class=\"BoxHeader\">\r\n          01 报告描述\r\n        </div>\r\n        <div class=\"cellStyle\">\r\n          <ul>\r\n            <li v-for=\"(item, key) in content.description\" :key=\"key\">\r\n              {{ item }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n                </div>  2023 9-4   沈老师  暂时注释-->\r\n      <div class=\"detail-container\">\r\n        <template v-if=\"!preViewData.isWechat\">\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">01 事件走势</div>\r\n            <span\r\n              style=\"position: relative; top: 20px; left: 55px; font-size: 14px\"\r\n              v-if=\"content\"\r\n              >{{ content ? content.eventDesc : null }}</span\r\n            >\r\n            <div class=\"charts\" id=\"line\"></div>\r\n          </div>\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">02 热门文章</div>\r\n            <div class=\"tableStyle\">\r\n              <div>\r\n                <el-table\r\n                  size=\"mini\"\r\n                  :data=\"tableData\"\r\n                  border\r\n                  style=\"width: 92.5%\"\r\n                  :header-cell-style=\"{\r\n                    textAlign: 'center',\r\n                    backgroundColor: 'rgb(64, 158, 255)',\r\n                    color: '#ffff',\r\n                  }\"\r\n                  :cell-style=\"{}\"\r\n                >\r\n                  <el-table-column\r\n                    type=\"index\"\r\n                    label=\"序号\"\r\n                    width=\"60\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"cnTitle\"\r\n                    label=\"标题\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span @click=\"openNewView(scope.row)\" class=\"tableTitle\">\r\n                        {{ scope.row.cnTitle }}\r\n                      </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishType\"\r\n                    label=\"平台类型\"\r\n                    width=\"130\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{\r\n                        scope.row.sourceType == 1\r\n                          ? \"微信公众号\"\r\n                          : null || scope.row.sourceType == 2\r\n                          ? \"网站\"\r\n                          : null || scope.row.sourceType == 3\r\n                          ? \"手动录入\"\r\n                          : null\r\n                      }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"sourceName\"\r\n                    label=\"媒体来源\"\r\n                    width=\"300\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishTime\"\r\n                    label=\"发布时间\"\r\n                    width=\"180\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{ formatDate(scope.row.publishTime) }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"preViewData.isWechat == 1\">\r\n          <div class=\"title\" style=\"font-weight: 600; font-size: 20px\">\r\n            {{ preViewData.reportName }}报送条目\r\n          </div>\r\n          <!-- <div class=\"title\" style=\"font-weight: 100;font-size: 32px;margin: 20px auto;width: 50%;font-family: cursive;\">\r\n          （★条目为“境外媒体关于我国的文章”，已由国内翻译、转载，并审核溯源可靠、内容准确。）</div> -->\r\n          <div\r\n            v-for=\"(item, index) in wechatList\"\r\n            :key=\"index\"\r\n            class=\"worldStyle\"\r\n          >\r\n            <p class=\"cnSummary\">\r\n              <!-- {{ index + 1 + \".微信公众号\" + item.sourceName }} -->\r\n              {{ index + 1 + \".\" + item.sourceName }}\r\n              {{\r\n                item.publishTime\r\n                  ? new Date(item.publishTime).getMonth() +\r\n                    1 +\r\n                    \"月\" +\r\n                    new Date(item.publishTime).getDate() +\r\n                    \"日\"\r\n                  : \"\"\r\n              }}\r\n              {{ \"报道:\" + (item.cnSummary ? item.cnSummary : \"暂无摘要\") }}\r\n            </p>\r\n            <p class=\"link\" @click=\"openNewViewWechat(item)\">\r\n              {{ \"(\" + (item.shortUrl ? item.shortUrl : \"暂无链接\") + \")\" }}\r\n            </p>\r\n          </div>\r\n        </template>\r\n        <div class=\"docx-container\">\r\n          <div ref=\"file\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { renderLineCharts, renderAnnular } from \"@/utils/renderLine.js\";\r\nimport { antiShake } from \"@/utils/utils.js\";\r\nimport { formatDate } from \"@/utils/index.js\";\r\nimport * as echarts from \"echarts\";\r\nimport API from \"@/api/ScienceApi/briefing.js\";\r\n// 引入docx-preview插件\r\nlet docx = require(\"docx-preview\");\r\nexport default {\r\n  props: {\r\n    preViewData: {\r\n      required: true,\r\n      type: Object,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      downloading: false,\r\n      myChart: null,\r\n      infoData: {},\r\n      content: {\r\n        detectionTime: \"\",\r\n      },\r\n      xAxis: {},\r\n      timer: null,\r\n      wechatList: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.formatDate = formatDate;\r\n  },\r\n  created() {\r\n    if (this.preViewData.flag) {\r\n      this.downloadBrieFing();\r\n    }\r\n\r\n    /* 微信公众号模板 */\r\n    if (this.preViewData.isWechat == 1) {\r\n      this.StatisticalList();\r\n    } else if (this.preViewData.isWechat == 2) {\r\n      this.getWorld();\r\n    } else {\r\n      this.getInfoData();\r\n    }\r\n  },\r\n  methods: {\r\n    renderLineCharts() {\r\n      let data = this.chrtsDataHandle();\r\n      new renderLineCharts({\r\n        dom: \"line\",\r\n        data: data,\r\n        titleShow: false,\r\n        xAxis: this.xAxis,\r\n      }).render();\r\n      // this.renderAnnular()\r\n    },\r\n    async getWorld() {\r\n      await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n        (res) => {\r\n          docx.renderAsync(res, this.$refs.file); // 渲染到页面\r\n          this.$store.commit(\"app/set_Loding\", false);\r\n        }\r\n      );\r\n    },\r\n    renderBar() {\r\n      var chartDom = document.getElementById(\"bar\");\r\n      this.myChart = echarts.init(chartDom);\r\n      var option;\r\n      option = {\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 250,\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"shadow\",\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          boundaryGap: [0, 0.01],\r\n          data: [\"微博\", \"微信\", \"今日头条\", \"网易新闻\", \"腾讯新闻\"],\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true,\r\n        },\r\n        series: [\r\n          {\r\n            data: [120, 200, 150, 80, 70, 110, 130],\r\n            type: \"bar\",\r\n          },\r\n        ],\r\n      };\r\n      option && this.myChart.setOption(option);\r\n    },\r\n    async downloadBrieFing(flag) {\r\n      this.$store.commit(\"app/set_Loding\", true);\r\n      /* 使用微信公众号模板 */\r\n      if (this.preViewData.isWechat) {\r\n        await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n          (res) => {\r\n            if (this.preViewData.isWechat == 1) {\r\n              this.downLoadXls(res);\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            } else if (this.preViewData.isWechat == 2) {\r\n              let a = document.createElement(\"a\");\r\n              a.href = window.URL.createObjectURL(res);\r\n              a.download = \"briefing.docx\";\r\n              a.click();\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            }\r\n          }\r\n        );\r\n      } else {\r\n        /* 使用普通模板 */\r\n        let dom = document.querySelector(\"#brieFing\");\r\n\r\n        if (dom) {\r\n          this.$store.commit(\"app/set_Loding\", true);\r\n          this.getPdf(dom);\r\n          setTimeout(() => {\r\n            this.$store.commit(\"app/set_Loding\", false);\r\n          }, 2000);\r\n        } else {\r\n          this.timer = setInterval(() => {\r\n            dom = document.querySelector(\"#brieFing\");\r\n            if (dom) {\r\n              this.getPdf(dom);\r\n              this.switchPreview();\r\n              this.$message({ message: \"下载成功\", type: \"success\" });\r\n            }\r\n          }, 2000);\r\n        }\r\n      }\r\n    },\r\n    ReportStatisics() {\r\n      this.$emit(\"ReportStatisics\");\r\n    },\r\n    /* 获取详情数据 */\r\n    async getInfoData() {\r\n      let res;\r\n      /* 当前为生成前预览 */\r\n      if (this.preViewData.previewData) {\r\n        this.content = JSON.parse(this.preViewData.previewData);\r\n        this.infoData.title = this.content.title;\r\n        this.tableData = this.content.hotList;\r\n        this.description = this.content.description;\r\n        setTimeout(() => {\r\n          try {\r\n            this.renderLineCharts();\r\n          } catch (error) {}\r\n        }, 200);\r\n        return;\r\n      }\r\n      res = await API.briefingInfo(this.preViewData.briefingId);\r\n      if (res.code == 200) {\r\n        this.infoData = res.data;\r\n        this.content = JSON.parse(res.data.content);\r\n        if (this.content) {\r\n          this.tableData = this.content.hotList;\r\n          this.description = this.content.description;\r\n        }\r\n      } else {\r\n        this.$message({ message: \"数据获取失败\", type: \"error\" });\r\n      }\r\n      try {\r\n        this.renderLineCharts();\r\n      } catch (error) {}\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewViewWechat(item) {\r\n      if (!item.shortUrl) return;\r\n      if (item.sourceType == 1) {\r\n        window.open(item.shortUrl);\r\n      } else if (item.sourceType == 2) {\r\n        window.open(item.originalUrl);\r\n      }\r\n    },\r\n    /* 图表数据处理 */\r\n    chrtsDataHandle() {\r\n      let WeArr = [],\r\n        WyArr = [],\r\n        Xdata = [],\r\n        end_Time,\r\n        start_Time,\r\n        Year,\r\n        endYear;\r\n      /* 当前是报告详情 */\r\n      if (!this.preViewData.previewData && this.content.dateType) {\r\n        switch (this.content.dateType) {\r\n          case \"hour\" /* 按小时计算 */:\r\n            WeArr.length = 24;\r\n            WyArr.length = 24;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 处理x轴数据 */\r\n            for (let i = 0; i < 24; i++) {\r\n              Xdata.push(`${i.toString().length == 1 ? \"0\" + i : i}:00`);\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WyArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WeArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"day\":\r\n            (end_Time = Number(this.content.endTime.slice(8, 10))),\r\n              (start_Time = Number(this.content.startTime.slice(8, 10))),\r\n              (Year = this.content.startTime.slice(0, 7));\r\n            endYear = this.content.endTime.slice(0, 7);\r\n            /* 跨越两个月的情况 */\r\n            let end = Number(this.content.endTime.slice(5, 7)),\r\n              start = Number(this.content.startTime.slice(5, 7)),\r\n              num = 30 - Number(start_Time);\r\n            if (end > start) {\r\n              end_Time = end_Time + num;\r\n            }\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            /* 数据填充 */\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 循环数据 */\r\n            let a,\r\n              i = 1;\r\n            while (Xdata.length < end_Time) {\r\n              a = i;\r\n              let item;\r\n              if (start_Time <= 30) {\r\n                i = start_Time;\r\n              } else if (start_Time <= 31) {\r\n                i = 1;\r\n                a = 1;\r\n              }\r\n              if (start_Time > 30) {\r\n                item = endYear + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              } else {\r\n                item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              }\r\n              Xdata.push(item);\r\n              i = a;\r\n              ++start_Time;\r\n              ++i;\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WeArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"month\":\r\n            end_Time = Number(this.content.endTime.slice(5, 7));\r\n            start_Time = Number(this.content.startTime.slice(5, 7));\r\n            Year = this.content.startTime.slice(0, 4);\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            for (let i = start_Time; i <= end_Time; i++) {\r\n              let item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              Xdata.push(item);\r\n            }\r\n\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              let index = Xdata.lastIndexOf(Year + \"-\" + key);\r\n              WeArr[index] = this.content.wxTrendCount[item];\r\n            });\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      } else {\r\n        /* 当前是草稿详情 */\r\n\r\n        let Wx = this.content.wxTrendCount,\r\n          WEB = this.content.wyTrendCount;\r\n\r\n        Object.keys(Wx).forEach((item) => {\r\n          WeArr.push(Wx[item]);\r\n          Xdata.push(item);\r\n        });\r\n        Object.keys(WEB).forEach((item) => {\r\n          WyArr.push(WEB[item]);\r\n        });\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      }\r\n      return [\r\n        {\r\n          name: \"微信\",\r\n          data: WeArr,\r\n          type: \"line\",\r\n        },\r\n        {\r\n          name: \"网站\",\r\n          data: WyArr,\r\n          type: \"line\",\r\n        },\r\n      ];\r\n    },\r\n    /* 文件流解码 */\r\n    downLoadXls(res) {\r\n      let fileName = this.preViewData.reportName;\r\n      if (\"download\" in document.createElement(\"a\")) {\r\n        const a = document.createElement(\"a\"); //创建一个a标签\r\n        a.download = fileName + \".docx\"; //指定文件名称\r\n        a.style.display = \"none\"; //页面隐藏\r\n        a.href = URL.createObjectURL(res); // href用于下载地址\r\n        document.body.appendChild(a); //插到页面上\r\n        a.click(); //通过点击触发\r\n        URL.revokeObjectURL(a.href); //释放URL 对象\r\n        document.body.removeChild(a); //删掉a标签\r\n      } else {\r\n        //IE10 + 下载\r\n        navigator.msSaveBlob(res, fileName);\r\n      }\r\n    },\r\n    switchPreview() {\r\n      this.$emit(\"switchPreview\");\r\n    },\r\n    /* 文章统计列表 */\r\n    async StatisticalList() {\r\n      this.dialogTableVisible = true;\r\n      let res = await API.statistics({\r\n        pageSize: 99,\r\n        pageNum: this.pageNum1,\r\n        reportId: this.preViewData.briefingId,\r\n      });\r\n      if (res.code == 200) {\r\n        this.wechatList = res.rows;\r\n      }\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewView(item) {\r\n      if (item.sourceType == 1) {\r\n        if (item.shortUrl) {\r\n          window.open(item.shortUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      } else if (item.sourceType == 2) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      }\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tableTitle:hover {\r\n  color: #228fd3;\r\n  border-bottom: solid 1px #228fd3;\r\n}\r\n\r\n.PreviewMain {\r\n  .topTool {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    margin-bottom: 10px;\r\n\r\n    .callback {\r\n      font-size: 16px;\r\n      color: rgb(8, 166, 240);\r\n      cursor: pointer;\r\n    }\r\n\r\n    .iconCallback {\r\n      font-size: 18px;\r\n      color: rgb(8, 166, 240);\r\n    }\r\n  }\r\n\r\n  width: 90%;\r\n  overflow: hidden;\r\n  height: calc(100vh - 100px);\r\n  margin: 0 auto;\r\n  padding-top: 20px;\r\n  border: solid 1px #efefef;\r\n  box-shadow: 0px 2px 11px 9px #efefef;\r\n  margin-top: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .title {\r\n    text-align: center;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n\r\n    h1 {\r\n      font-size: 30px;\r\n    }\r\n  }\r\n\r\n  .describe {\r\n    width: 98%;\r\n    min-height: 200px;\r\n    margin: 15px auto;\r\n    box-shadow: 4px 6px 4px 2px #efefef;\r\n\r\n    .BoxHeader {\r\n      height: 40px;\r\n      line-height: 40px;\r\n      padding-left: 15px;\r\n      width: 100%;\r\n      border-top: solid 1px #e0dfdf;\r\n      border-left: solid 1px #e0dfdf;\r\n      border-right: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .cellStyle {\r\n      width: 100%;\r\n      height: 160px;\r\n      border: solid 1px #e0dfdf;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      p {\r\n        width: 90%;\r\n        font-size: 14px;\r\n        margin: 0 auto;\r\n      }\r\n\r\n      ul {\r\n        font-size: 14px;\r\n        line-height: 25px;\r\n\r\n        li {\r\n          list-style: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin-left: 15px;\r\n    }\r\n\r\n    .charts {\r\n      width: 100%;\r\n      height: 300px;\r\n      border: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      min-height: 400px;\r\n      border: solid 1px #e0dfdf;\r\n\r\n      div {\r\n        margin: 20px auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.world {\r\n  width: 80%;\r\n  margin: 0 auto;\r\n  min-height: 1200px;\r\n  background-color: #228fd3;\r\n}\r\n\r\n.worldStyle {\r\n  width: 50%;\r\n  margin: 0 auto;\r\n  text-overflow: clip;\r\n\r\n  .cnSummary {\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n    text-indent: 2em;\r\n  }\r\n\r\n  .link {\r\n    margin-top: -20px;\r\n    font-weight: 400;\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n  }\r\n\r\n  .link:hover {\r\n    color: #228fd3;\r\n    border-bottom: solid #228fd3 1px;\r\n  }\r\n}\r\n\r\n.detail-container {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n<style scoped>\r\n.docx-container ::v-deep .docx-wrapper {\r\n  background-color: #fff;\r\n  padding: 20px 20px;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx {\r\n  width: 55vw !important;\r\n  padding: 0rem !important;\r\n  min-height: auto !important;\r\n  box-shadow: none;\r\n  margin-bottom: 0;\r\n  line-height: 50px;\r\n  overflow-y: scroll;\r\n  height: 100vh;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n</style>\r\n"]}]}