<template>
  <div
    v-loading="globalLoading"
    element-loading-text="数据加载中"
    v-if="funEsSeach"
  >
    <splitpanes class="default-theme">
      <pane
        class="leftLink"
        ref="leftLink"
        min-size="20"
        max-size="50"
        size="28"
        v-if="!$route.query.domain"
      >
        <TreeTable
          ref="treeTable"
          :data="treeDataTransfer"
          :total="treeTotal"
          :current-page="treeCurrentPage"
          :page-size="treePageSize"
          :loading="loading"
          :selected-sources="savedCheckboxData"
          row-key="id"
          @selection-change="handleSelectionChange"
          @reset="handleReset"
          @size-change="handleTreePageSizeChange"
          @current-change="handleTreeCurrentChange"
          @filter-search="handleFilterSearch"
          @classify-change="handleClassifyChange"
          @country-change="handleCountryChange"
        />
      </pane>
      <pane
        :min-size="$route.query.domain ? '100' : '50'"
        :max-size="$route.query.domain ? '100' : '80'"
        :size="$route.query.domain ? '100' : '72'"
      >
        <div
          class="rightMain"
          style="margin-left: 0; overflow-y: auto"
          ref="rightMain"
          v-loading="tableLoading"
          element-loading-text="数据加载中"
        >
          <div class="toolBox" v-if="!$route.query.domain">
            <!-- <div class="title" :style="{ height: ActiveData.title ? '' : '50px' }">
              <p v-if="ActiveData.title">{{ ActiveData.title }}</p>
              <p v-else></p>
            </div> -->
            <div class="mainTool">
              <p>
                发布日期:
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 1 ? 'primary' : ''"
                  @click="SeachData.timeRange = 1"
                  >今天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 2 ? 'primary' : ''"
                  @click="SeachData.timeRange = 2"
                  >近2天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 4 ? 'primary' : ''"
                  @click="SeachData.timeRange = 4"
                  >近7天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 5 ? 'primary' : ''"
                  @click="SeachData.timeRange = 5"
                  >近30天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 10 ? 'primary' : ''"
                  @click="SeachData.timeRange = 10"
                  >全部</el-button
                >
                <!-- <el-button
                  v-if="$route.query.menuType && $route.query.menuType === '8'"
                  size="mini"
                  :type="SeachData.timeRange == 7 ? 'primary' : ''"
                  @click="SeachData.timeRange = 7"
                  >近三个月</el-button
                >
                <el-button
                  v-if="$route.query.menuType && $route.query.menuType === '8'"
                  size="mini"
                  :type="SeachData.timeRange == 8 ? 'primary' : ''"
                  @click="SeachData.timeRange = 8"
                  >近半年</el-button
                > -->
                <el-button
                  size="mini"
                  :type="SeachData.timeRange == 6 ? 'primary' : ''"
                  @click="SeachData.timeRange = 6"
                  >自定义</el-button
                >
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.customDay"
                  v-if="SeachData.timeRange == 6"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <p>
                采集日期:
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 0 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 0"
                  >24小时</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 1 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 1"
                  >今天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 2 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 2"
                  >近2天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 4 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 4"
                  >近7天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 5 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 5"
                  >近30天</el-button
                >
                <el-button
                  size="mini"
                  :type="SeachData.collectionDateType == 6 ? 'primary' : ''"
                  @click="SeachData.collectionDateType = 6"
                  >自定义</el-button
                >
                <el-date-picker
                  value-format="yyyy-MM-dd HH:mm:ss"
                  v-model="SeachData.collectionTime"
                  v-if="SeachData.collectionDateType == 6"
                  style="display: inline-block; width: 320px; margin-left: 10px"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  unlink-panels
                  clearable
                ></el-date-picker>
              </p>
              <div style="display: flex">
                <p style="margin-right: 30px; margin-top: 0">
                  <span
                    style="
                      width: 60px;
                      display: inline-block;
                      text-align: right;
                      margin-right: 5px;
                    "
                    >小信优选:</span
                  >
                  <el-radio-group v-model="SeachData.isTechnology" size="small">
                    <el-radio-button
                      v-for="dict in dict.type.is_technology"
                      :label="dict.value"
                      :key="'is_technology' + dict.value"
                      >{{ dict.label }}</el-radio-button
                    >
                    <el-radio-button :label="null" :key="'is_technology3'"
                      >全部</el-radio-button
                    >
                  </el-radio-group>
                </p>
                <p style="margin-top: 0">
                  <span
                    style="
                      width: 60px;
                      display: inline-block;
                      text-align: right;
                      margin-right: 5px;
                    "
                    >小信精选:</span
                  >
                  <el-radio-group v-model="SeachData.emotion" size="small">
                    <el-radio-button :label="'1'" :key="'is_emotion1'"
                      >选中</el-radio-button
                    >
                    <el-radio-button :label="'0'" :key="'is_emotion0'"
                      >全部</el-radio-button
                    >
                  </el-radio-group>
                </p>
              </div>
              <div class="keyword">
                <span
                  style="
                    width: 60px;
                    display: inline-block;
                    text-align: right;
                    margin-right: 5px;
                  "
                  >关键词:</span
                >
                <el-input
                  ref="keywordRef"
                  placeholder="请输入关键词,使用逗号分割(英文)"
                  style="width: 430px"
                  v-model="SeachData.keyword"
                  @focus="showHistoryList()"
                  @blur="hideHistoryList()"
                >
                </el-input>
                <el-button
                  type="primary"
                  size="mini"
                  @click="funEsSeach()"
                  :loading="buttonDisabled"
                  style="margin-left: 10px; height: 36px"
                  >搜索</el-button
                >
                <div class="history" v-show="showHistory">
                  <div
                    class="historyItem"
                    v-for="(history, index) in historyList"
                    :key="index"
                    v-loading="historyLoading"
                  >
                    <div @click="keywordsChange(history)" class="historyText">
                      {{ history.keyword }}
                    </div>
                    <el-button
                      type="text"
                      @click="removeHistory(history, 1)"
                      style="color: #999; font-size: 12px"
                      >删除</el-button
                    >
                  </div>
                  <div class="historyItem">
                    <el-button type="text" @click="moreHistory()"
                      >更多</el-button
                    >
                    <el-button
                      type="text"
                      @click="clearHistory()"
                      style="color: #999; font-size: 12px"
                      >清空</el-button
                    >
                  </div>
                </div>
              </div>
              <div class="keyword-tip">
                *支持按照多个关键词检索，“与”使用“,”分隔，“或”使用“|”分割
              </div>
            </div>
            <!-- <div class="btn">
              <el-button size="mini" @click="resetting">重置</el-button>
              <el-button
                type="primary"
                size="mini"
                @click="funEsSeach('filter')"
                :loading="buttonDisabled"
                >搜索</el-button
              >
            </div> -->
          </div>
          <MainArticle
            v-if="$route.query.menuType"
            :flag="'artificialIntelligence'"
            :currentPage="currentPage"
            :pageSize="pageSize"
            :total="total"
            :ArticleList="ArticleList"
            :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
            @Refresh="funEsSeach()"
            :SeachData="SeachData"
            ref="mainArticle"
          ></MainArticle>
          <MainArticle
            v-else
            :flag="'MonitorUse'"
            :currentPage="currentPage"
            :pageSize="pageSize"
            :total="total"
            :ArticleList="ArticleList"
            :keywords="SeachData.keyword"
            @handleCurrentChange="handleCurrentChange"
            @handleSizeChange="handleSizeChange"
            @Refresh="funEsSeach()"
            :SeachData="SeachData"
            ref="mainArticle"
          ></MainArticle>
        </div>
      </pane>
    </splitpanes>

    <el-dialog
      title="关键词历史"
      :visible.sync="dialogVisible1"
      width="570px"
      :close-on-click-modal="false"
    >
      <div class="history" v-loading="historyLoading">
        <div
          class="historyItem"
          v-for="(history, index) in historyList1"
          :key="index"
        >
          <div @click="keywordsChange(history)" class="historyText">
            {{ history.keyword }}
          </div>
          <el-button type="text" @click="removeHistory(history, 2)"
            >删除</el-button
          >
        </div>
      </div>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        :page.sync="queryParams1.pageNum"
        :limit.sync="queryParams1.pageSize"
        :background="false"
        @pagination="getArticleHistory1"
        :layout="'total, prev, pager, next'"
      />
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/ScienceApi/index.js";
import topSeach from "@/views/components/topSeach.vue";
import MainArticle from "../components/MainArticle.vue";
import {
  listArticleHistory,
  delArticleHistory,
  addArticleHistory,
  cleanArticleHistory,
} from "@/api/article/articleHistory";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import TreeTable from "@/components/TreeTable/index.vue";

export default {
  components: { topSeach, MainArticle, Splitpanes, Pane, TreeTable },
  dicts: ["is_technology"],
  data() {
    return {
      width: "258",
      isReSize: false,
      /* 文章主体组件数据 */
      currentPage: 1,
      pageSize: 50,
      total: 0,
      ArticleList: [],
      treeDataTransfer: [],
      checkList: [],
      /* 树形分页数据 */
      treeCurrentPage: 1,
      treePageSize: 100,
      treeTotal: 0,

      /* 搜索组件数据 */
      SeachData: {
        metaMode: "" /* 匹配模式 */,
        keyword: "" /* 关键词 */,
        timeRange: 4 /* 时间范围 */,
        customDay: [] /* 自定义天 */,
        collectionDateType: null /* 时间范围 */,
        collectionTime: [] /* 自定义天 */,
        isTechnology: "1",
        sortMode: "0",
        emotion: "0",
        hasCache: "0",
      } /* 搜索条件 */,
      /* 排序模式 - 单独提取，避免触发SeachData的深度监听 */
      buttonDisabled: false /* 按钮防抖 */,
      ActiveData: {},
      seniorSerchFlag: false /* 普通检索或高级检索 */,
      areaList: [] /* 国内地区 */,
      countryList: [] /* 国家或地区 */,
      KeList: [],
      funEsSeach: null,
      treeQuery: {
        filterwords: "", // 添加树搜索关键字
      },
      domainList: [],
      industryList: [],
      showHistory: false,
      historyList: [],
      historyTimeout: null,
      dialogVisible1: false,
      historyLoading: false,
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      total1: 0,
      historyList1: [],
      initializationCompleted: false, // 标记初始化是否完成
      // 从Wechat.vue同步的属性
      loading: false, // 树组件loading状态
      tableLoading: false, // 表格loading状态
      isQuerying: false, // 查询防抖
      queryDebounceTimer: null, // 查询防抖定时器
      isRightFilter: false, // 标记右侧筛选条件是否发生变化
      isLeftReset: false, // 标记左侧树是否重置
      selectedClassify: null, // 选中的数据源分类
      selectedCountry: null, // 选中的国家
      /* 保存的勾选数据（永久保存，只有特定操作才更新） */
      savedCheckboxData: [],
      globalLoading: false,
    };
  },
  async created() {
    try {
      // 初始化funEsSeach方法
      this.funEsSeach = this.EsSeach;

      // 先加载基础数据
      this.getArticleHistory();

      // if (this.$route.query.menuType && this.$route.query.menuType === "8") {
      //   this.SeachData.timeRange = 7;
      // }

      // 加载树数据和内容数据
      await this.initializeData();

      // 标记初始化完成，这样watch监听器才会开始工作
      this.initializationCompleted = true;
    } catch (error) {
      console.error("组件初始化失败:", error);
      this.$message.error("初始化失败，请刷新页面重试");
    }
  },

  mounted() {
    // TreeTable 组件不需要特殊的状态检查
  },
  watch: {
    // 监听筛选条件变化
    "SeachData.timeRange": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.collectionDateType": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.isTechnology": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
    "SeachData.emotion": {
      handler(newVal, oldVal) {
        if (!this.initializationCompleted || newVal === oldVal) return;
        this.handleFilterChange();
      },
    },
  },
  methods: {
    // 初始化数据
    async initializeData() {
      try {
        // 加载文章列表（内部已经处理了 tableLoading）
        this.queryArticleList();
        // 加载树数据
        await this.queryTreeData();
        // 等待树组件完全渲染
        await this.$nextTick();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化失败，请刷新页面重试");
      }
    },

    // TreeTable 组件事件处理方法

    // 处理选择变化
    handleSelectionChange(selectedData, operationType) {
      if (operationType === "row-click" || operationType === "clear-all") {
        // 点击行（单选）或取消所有选中：直接替换，不需要追加去重
        this.checkList = [...selectedData];
        this.savedCheckboxData = [...selectedData];
      } else if (
        operationType === "checkbox-change" ||
        operationType === "select-all"
      ) {
        // 点击勾选框（多选）或全选：需要正确处理选中和取消选中
        // 先从保存的数据中移除当前页面的所有数据
        const currentPageIds = this.treeDataTransfer.map(
          (item) => item.sourceSn
        );
        const filteredCheckList = this.checkList.filter(
          (item) => !currentPageIds.includes(item.sourceSn)
        );
        const filteredSavedData = this.savedCheckboxData.filter(
          (item) => !currentPageIds.includes(item.sourceSn)
        );

        // 然后添加当前页面新选中的数据
        const combinedCheckList = [...filteredCheckList, ...selectedData];
        const combinedSavedData = [...filteredSavedData, ...selectedData];

        // 对合并后的数据进行去重处理
        this.checkList = this.deduplicateBySourceSn(combinedCheckList);
        this.savedCheckboxData = this.deduplicateBySourceSn(combinedSavedData);
      } else {
        // 默认情况：直接替换（兼容性处理）
        this.checkList = [...selectedData];
        this.savedCheckboxData = [...selectedData];
      }

      // 重置页码并查询内容
      this.currentPage = 1;
      this.scrollToTopImmediately();
      if (!this.isRightFilter) {
        this.queryArticleList("sourceItemChanged");
      }
    },

    // 根据sourceSn去重的辅助方法
    deduplicateBySourceSn(dataArray) {
      const seen = new Set();
      return dataArray.filter((item) => {
        if (seen.has(item.sourceSn)) {
          return false;
        }
        seen.add(item.sourceSn);
        return true;
      });
    },

    // 处理重置
    handleReset() {
      // 先清空过滤关键字，避免触发 handleFilterSearch
      this.treeQuery.filterwords = "";
      this.selectedClassify = null;
      this.selectedCountry = null;

      // 然后设置重置标记
      this.isLeftReset = true;

      // 清空选中状态
      this.checkList = [];

      // 清空保存的勾选数据（永久保存）
      this.savedCheckboxData = [];

      // 重置页码并查询列表数据
      this.currentPage = 1;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";
      this.scrollToTopImmediately();

      // 重新查询树和列表
      this.queryTreeAndList();
    },

    // 处理筛选条件变化 - 来自右侧筛选条件的变化
    handleFilterChange() {
      this.isRightFilter = true; // 标记右侧筛选条件发生变化

      // 不再保存当前选中状态，使用永久保存的勾选数据
      // 永久保存的勾选数据会在查询后自动恢复

      // 重置分页到第一页
      this.currentPage = 1;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "0";

      // 滚动到顶部
      this.scrollToTopImmediately();

      // 同时查询树和列表
      this.queryTreeAndList();
    },

    // 同时查询树和列表 - 直接从Wechat.vue复制
    async queryTreeAndList() {
      try {
        // 保存当前的永久勾选数据，避免在查询过程中丢失
        const savedData = [...this.savedCheckboxData];

        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数
        if (savedData && savedData.length > 0) {
          this.checkList = [...savedData];
        } else {
          // 如果没有永久保存的勾选数据，清空选中状态
          this.checkList = [];
        }

        // 同时查询树数据和右侧列表（保持性能优势）
        await Promise.all([
          this.queryTreeData(),
          this.queryArticleList(), // queryArticleList 内部已经处理了 tableLoading
        ]);

        // 确保永久保存的勾选数据不会丢失
        this.savedCheckboxData = savedData;

        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.restoreFromSavedCheckboxData();
        }

        // 查询完成后重置右侧筛选标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      } catch (error) {
        console.error("同时查询树和列表失败:", error);
        this.$message.error("查询失败，请重试");
        // 即使出错也要重置标记
        this.isRightFilter = false;
        setTimeout(() => {
          this.isLeftReset = false;
        }, 300);
      }
    },

    // 恢复选中数据源的方法已删除，使用永久保存的勾选数据

    // 从永久保存的勾选数据恢复选中状态（仅处理界面选中状态）
    restoreFromSavedCheckboxData() {
      if (!this.savedCheckboxData || this.savedCheckboxData.length === 0) {
        return;
      }

      // 在当前树数据中查找匹配的项
      const matchedItems = [];
      this.savedCheckboxData.forEach((savedItem) => {
        const foundItem = this.treeDataTransfer.find(
          (treeItem) => treeItem.sourceSn === savedItem.sourceSn
        );
        if (foundItem) {
          matchedItems.push(foundItem);
        }
      });

      if (matchedItems.length > 0) {
        // 更新选中列表（此时 checkList 已经在查询前恢复过了）
        this.checkList = matchedItems;
        // 通知 TreeTable 组件恢复界面选中状态（不触发事件）
        this.$nextTick(() => {
          if (this.$refs.treeTable) {
            this.$refs.treeTable.restoreSelectionSilently(matchedItems);
          }
        });
      } else {
        // 如果没有匹配项，清空选中状态
        this.checkList = [];
      }
    },

    // 保存当前选中状态的方法已删除，使用永久保存的勾选数据

    // 查询树数据并从永久保存的勾选数据恢复选中状态（用于关键字过滤）
    async queryTreeDataWithRestoreFromSaved() {
      try {
        // 如果有永久保存的勾选数据，先临时恢复 checkList 以便查询时带上参数
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.checkList = [...this.savedCheckboxData];
        } else {
          this.checkList = [];
        }

        // 查询树数据
        await this.queryTreeData();

        // 查询完成后，如果有永久保存的勾选数据，静默恢复界面选中状态
        if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
          this.restoreFromSavedCheckboxData();
        }
      } catch (error) {
        console.error(
          "查询树数据并从永久保存的勾选数据恢复选中状态失败:",
          error
        );
      }
    },

    // 分页处理
    handleCurrentChange(current) {
      this.currentPage = current;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.scrollToTopImmediately();
      this.queryArticleList();
    },

    // 查询树数据
    async queryTreeData() {
      this.loading = true;
      try {
        const params = {
          pageNum: this.treeCurrentPage,
          pageSize: this.treePageSize,
          platformType: 1,
          id: this.$route.query.menuType ? "1" : this.$route.query.id,
          m: 1,
          dateType:
            this.SeachData.timeRange != 6 ? this.SeachData.timeRange : "",
          startTime: this.SeachData.customDay
            ? this.SeachData.customDay[0]
            : "",
          endTime: this.SeachData.customDay ? this.SeachData.customDay[1] : "",
          collectionDateType:
            this.SeachData.collectionDateType != 6
              ? this.SeachData.collectionDateType
              : "",
          collectionStartTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[0]
            : "",
          collectionEndTime: this.SeachData.collectionTime
            ? this.SeachData.collectionTime[1]
            : "",
          keywords: this.SeachData.keyword,
          isTechnology: this.SeachData.isTechnology,
          emotion: this.SeachData.emotion,
          // 添加关键字过滤参数
          filterwords: this.treeQuery.filterwords || "",
          // 添加数据源分类参数
          thinkTankClassification: this.selectedClassify,
          // 添加国家筛选参数
          countryOfOrigin: this.selectedCountry,
          hasCache: this.SeachData.hasCache,
        };

        if (this.$route.query.menuType) {
          params.menuType = this.$route.query.menuType;
        }

        const res = await api.monitoringMedium(params);

        if (res.code === 200) {
          const dataList = res.rows || [];
          const total = res.total || 0;

          const mapData = (data) =>
            data.map((item, index) => ({
              id: `${
                item.sourceSn || "unknown"
              }_${index}_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 11)}`, // 确保绝对唯一性
              label: item.cnName,
              count: item.articleCount || 0,
              orderNum: item.orderNum,
              country: item.countryOfOrigin || null,
              sourceSn: item.sourceSn,
              url: item.url || null,
            }));

          this.treeDataTransfer = mapData(dataList);
          this.treeTotal = total;
        }
      } catch (error) {
        console.error("查询树数据失败:", error);
        this.$message.error("获取数据源失败");
      } finally {
        this.loading = false;
      }
    },

    // 查询文章列表（带防抖）
    async queryArticleList(flag) {
      // 防止重复查询
      if (this.isQuerying) {
        return;
      }

      if (!flag) {
        this.tableLoading = true;
      }

      // 清除之前的防抖定时器
      if (this.queryDebounceTimer) {
        clearTimeout(this.queryDebounceTimer);
      }

      // 设置防抖，300ms后执行查询
      this.queryDebounceTimer = setTimeout(async () => {
        try {
          if (flag === "sourceItemChanged") {
            this.globalLoading = true;
          }

          this.isQuerying = true;

          const params = {
            m: 1,
            pageNum: this.currentPage,
            pageSize: this.pageSize,
            id: this.$route.query.menuType ? "1" : this.$route.query.id,
            isSort: this.SeachData.sortMode,
            dateType:
              this.SeachData.timeRange != 6 ? this.SeachData.timeRange : "",
            startTime: this.SeachData.customDay
              ? this.SeachData.customDay[0]
              : "",
            endTime: this.SeachData.customDay
              ? this.SeachData.customDay[1]
              : "",
            collectionDateType:
              this.SeachData.collectionDateType != 6
                ? this.SeachData.collectionDateType
                : "",
            collectionStartTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[0]
              : "",
            collectionEndTime: this.SeachData.collectionTime
              ? this.SeachData.collectionTime[1]
              : "",
            keywords: this.SeachData.keyword,
            isTechnology: this.SeachData.isTechnology,
            emotion: this.SeachData.emotion,
            platformType: 1,
          };

          if (this.$route.query.menuType) {
            params.menuType = this.$route.query.menuType;
          }

          // 使用永久保存的勾选数据构建查询参数
          if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
            const data = this.savedCheckboxData.map((item) => item.label);
            const sourceSn = this.savedCheckboxData.map(
              (item) => item.sourceSn
            );

            params.weChatName = String(data);
            params.sourceSn = String(sourceSn);
          }

          // 记录关键词历史
          if (params.keywords) {
            addArticleHistory({ keyword: params.keywords, type: 2 }).then(
              () => {
                this.getArticleHistory();
              }
            );
          }

          let qykjdtParams;

          if (this.$route.query.domain) {
            qykjdtParams = {
              pageNum: this.currentPage,
              pageSize: this.pageSize,
              sourceSn: this.$route.query.domain,
              isSort: this.SeachData.sortMode,
            };
          }

          const res = this.$route.query.domain
            ? await api.qykjdtArticleList({ ...qykjdtParams })
            : await api.KeIntegration({ ...params });

          if (res.code == 200) {
            let articleList;

            if (this.$route.query.domain) {
              articleList = res.rows || [];
            } else {
              articleList = res.data.list || [];
            }

            // 去重逻辑：只有在没有关键词搜索时才进行去重
            if (
              !this.SeachData.keyword ||
              this.SeachData.keyword.trim() === ""
            ) {
              articleList = this.deduplicateArticles(articleList);
            }

            this.ArticleList = articleList;

            if (this.$route.query.domain) {
              this.total = res.total || 0;
            } else {
              this.total = res.data.total || 0;
            }

            // 如果有永久保存的勾选数据，恢复选中状态（静默恢复，不触发右侧查询）
            if (this.savedCheckboxData && this.savedCheckboxData.length > 0) {
              this.restoreFromSavedCheckboxData();
            }

            // 处理分页为空的情况
            if (
              this.ArticleList.length == 0 &&
              this.pageSize * (this.currentPage - 1) >= this.total &&
              this.total != 0
            ) {
              this.currentPage = Math.max(
                1,
                Math.ceil(this.total / this.pageSize)
              );
              // 重新查询
              await this.queryArticleList();
              return; // 重新查询时不要关闭loading
            }
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        } catch (error) {
          console.error("查询文章列表失败:", error);
          this.$message.error("查询失败，请重试");
        } finally {
          this.isQuerying = false;
          this.globalLoading = false;
          this.tableLoading = false; // 查询完成后关闭loading
          this.buttonDisabled = false;
        }
      }, 1000);
    },

    // 滚动到顶部
    scrollToTopImmediately() {
      this.$nextTick(() => {
        // 尝试多种滚动方式确保滚动成功
        const scrollBoxElement = document.querySelector(".scollBox");
        if (scrollBoxElement) {
          scrollBoxElement.scrollTop = 0;
        }

        // 如果MainArticle组件有scroll引用，也尝试滚动它
        if (
          this.$refs.mainArticle &&
          this.$refs.mainArticle.$refs &&
          this.$refs.mainArticle.$refs.scroll
        ) {
          this.$refs.mainArticle.$refs.scroll.scrollTop = 0;
        }

        // 滚动整个右侧区域
        const rightMain = document.querySelector(".rightMain");
        if (rightMain) {
          rightMain.scrollTop = 0;
        }
      });
    },
    async getArea() {
      try {
        const Response = await api.getAreaList();
        if (Response && Response.code == 200 && Response.data) {
          this.areaList = Response.data[0] || [];
          this.countryList = Response.data[1] || [];
        } else {
          console.warn("获取地区数据失败或数据为空");
        }
      } catch (err) {
        console.error("获取区域数据失败:", err);
        this.$message.error("地区数据获取失败");
      }
    },

    async getArticleHistory() {
      try {
        const res = await listArticleHistory({
          pageNum: 1,
          pageSize: 5,
          type: 2,
        });
        if (res && res.code === 200) {
          this.historyList = res.rows || [];
        }
      } catch (error) {
        console.error("获取历史记录失败:", error);
      }
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 添加EsSeach方法以兼容原有调用
    EsSeach(flag) {
      if (flag === "filter") {
        this.scrollToTopImmediately();
        // 筛选变化时同时查询树和列表
        this.queryTreeAndList();
      } else {
        // 其他情况只查询列表
        this.scrollToTopImmediately();
        this.queryArticleList();
      }
    },

    // 重置搜索条件 - 简化版本
    resetting() {
      try {
        this.SeachData = {
          metaMode: "" /* 匹配模式 */,
          keyword: "" /* 关键词 */,
          timeRange: "" /* 时间范围 */,
          customDay: [] /* 自定义天 */,
          collectionDateType: null /* 时间范围 */,
          collectionTime: [] /* 自定义天 */,
          isTechnology: "1",
          sortMode: "0",
          emotion: "0",
        };

        this.currentPage = 1;
        this.scrollToTopImmediately();
        this.queryTreeAndList();
      } catch (error) {
        console.error("重置搜索条件时出错:", error);
        this.$message.error("重置搜索条件失败");
      }
    },

    async removeHistory(item, type) {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (item && item.id) {
          await delArticleHistory([item.id]);

          if (type == 1) {
            if (this.$refs["keywordRef"]) {
              this.$refs["keywordRef"].focus();
            }
            await this.getArticleHistory();
          } else {
            await this.getArticleHistory();
            await this.getArticleHistory1();
          }
        }
      } catch (error) {
        console.error("删除历史记录时出错:", error);
        this.$message.error("删除历史记录失败");
      }
    },

    showHistoryList() {
      try {
        this.showHistory = true;
      } catch (error) {
        console.error("显示历史列表时出错:", error);
      }
    },

    hideHistoryList() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        this.historyTimeout = setTimeout(() => {
          this.showHistory = false;
        }, 500);
      } catch (error) {
        console.error("隐藏历史列表时出错:", error);
        this.showHistory = false; // 确保在出错时也能隐藏列表
      }
    },

    // 关键词历史选择 - 直接从Wechat.vue复制
    keywordsChange(item) {
      this.SeachData.keyword = item.keyword;
      this.dialogVisible1 = false;
      this.scrollToTopImmediately();
      this.currentPage = 1;
      // this.queryTreeAndList();
      this.queryArticleList();
    },

    async clearHistory() {
      try {
        if (this.historyTimeout) {
          clearTimeout(this.historyTimeout);
        }

        if (this.$refs["keywordRef"]) {
          this.$refs["keywordRef"].focus();
        }

        await cleanArticleHistory(2);
        await this.getArticleHistory();
      } catch (error) {
        console.error("清除历史记录时出错:", error);
        this.$message.error("清除历史记录失败");
      }
    },

    moreHistory() {
      try {
        this.historyLoading = true;
        this.getArticleHistory1();
        this.dialogVisible1 = true;
      } catch (error) {
        console.error("加载更多历史记录时出错:", error);
        this.historyLoading = false;
      }
    },

    async getArticleHistory1() {
      try {
        this.historyLoading = true;
        const response = await listArticleHistory({
          ...this.queryParams1,
          type: 2,
        });

        if (response) {
          this.historyList1 = response.rows || [];
          this.total1 = response.total || 0;
        }

        this.historyLoading = false;
      } catch (error) {
        console.error("获取文章历史记录时出错:", error);
        this.historyLoading = false;
        this.$message.error("获取搜索历史失败");
      }
    },

    openUrl(url) {
      window.open(url, "_blank");
    },

    // 处理过滤搜索（来自 TreeTable 组件）
    handleFilterSearch(keyword) {
      if (this.isLeftReset) {
        return;
      }

      // 不保存当前选中状态，使用永久保存的勾选数据

      // 更新查询参数中的 filterwords
      this.treeQuery.filterwords = keyword || "";

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";

      // 调用树数据查询接口并恢复选中状态（使用永久保存的勾选数据）
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 处理数据源分类变化（来自 TreeTable 组件）
    handleClassifyChange(classifyValue) {
      // 不保存当前选中状态，使用永久保存的勾选数据

      // 更新选中的分类
      this.selectedClassify = classifyValue;

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";

      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 处理国家筛选变化（来自 TreeTable 组件）
    handleCountryChange(countryValue) {
      // 不保存当前选中状态，使用永久保存的勾选数据

      // 更新选中的国家
      this.selectedCountry = countryValue;

      // 重置到第一页
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";

      // 只调用树数据查询接口并恢复选中状态，使用永久保存的勾选数据
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 处理树形分页页码变化
    handleTreeCurrentChange(page) {
      this.treeCurrentPage = page;
      this.SeachData.hasCache = "1";
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 处理树形分页每页大小变化
    handleTreePageSizeChange(size) {
      this.treePageSize = size;
      this.treeCurrentPage = 1;
      this.SeachData.hasCache = "1";
      this.queryTreeDataWithRestoreFromSaved();
    },

    // 添加缺失的openNewView方法
    openNewView(item) {
      window.open(
        `/expressDetails?id=${item.id}&docId=${item.docId}&sourceType=${item.sourceType}`,
        "_blank"
      );
    },

    // 添加缺失的handleHistoryPagination方法
    handleHistoryPagination() {
      this.getArticleHistory1();
    },

    // 文章去重方法
    deduplicateArticles(articles) {
      if (!articles || articles.length === 0) {
        return articles;
      }

      const titleMap = new Map();
      const result = [];

      // 统计相同标题的文章数量
      articles.forEach((article) => {
        // 去除HTML标签和所有空格来比较标题
        const cleanTitle = article.title
          ? article.title.replace(/<[^>]*>/g, "").replace(/\s+/g, "")
          : "";

        if (titleMap.has(cleanTitle)) {
          titleMap.get(cleanTitle).count++;
        } else {
          titleMap.set(cleanTitle, {
            article: { ...article },
            count: 1,
            originalTitle: article.title, // 保存原始标题（可能包含HTML标签）
          });
        }
      });

      // 生成去重后的文章列表
      titleMap.forEach(({ article, count, originalTitle }) => {
        if (count > 1) {
          // 如果有重复，在标题后面加上数量标记
          // 使用原始标题（保持HTML格式）
          article.title = `${originalTitle || ""}（${count}）`;
        }
        result.push(article);
      });

      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
.treeBox {
  width: 100%;
  height: calc(100vh - 176px);
  overflow-y: auto;
}

.tree-pagination {
  padding: 10px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  text-align: center;

  ::v-deep .el-pagination {
    .el-pagination__sizes {
      margin-top: -2px;
    }
  }
}

.treeMain {
  position: relative;
}

.treeQuery {
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;
    line-height: 24px;
    padding: 0 4px;
  }

  ::v-deep .el-input__suffix {
    // height: 20px;
    right: -2px;
    // top: 5px;
  }
}

.toolBox {
  min-height: 130px;
  height: auto;
  padding-bottom: 15px;
  background-color: rgb(255, 255, 255);
  // box-shadow: -1px 2px 15px #cecdcd;
  border-left: solid 1px rgb(221, 219, 219);

  .title {
    display: flex;
    justify-content: space-between;
    height: 70px;
    padding: 0 30px;
    font-size: 19px;
  }

  .mainTool {
    padding: 0 28px;
    font-size: 14px;
    color: rgb(58, 58, 58);
  }

  .mainToolOne {
    margin-top: 15px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    // align-items: center;
  }

  .mainToolTwo {
    display: flex;
    align-items: center;
    height: 40px;

    p {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .btn {
    margin: 15px 0 0 25px;
  }
}

.keyword {
  width: 100%;
  position: relative;
  margin-bottom: 10px;

  .history {
    width: 430px;
    position: absolute;
    background: #fff;
    z-index: 9999;
    left: 65px;
    border: 1px solid rgb(221, 219, 219);

    .historyItem {
      padding-left: 20px;

      .historyText {
        width: 450px;
        height: 34px;
        line-height: 34px;
      }

      &:nth-last-of-type(1) {
        padding-left: 0;

        ::v-deep .el-button--text {
          padding: 10px 20px;
        }
      }
    }
  }
}

.keyword-tip {
  font-size: 14px;
  color: #999;
  margin-left: 65px;
  line-height: 1;
}

.history {
  width: 530px;

  .historyItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    padding: 0 10px;
    overflow: hidden;

    .historyText {
      width: 350px;
      height: 34px;
      line-height: 34px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
