{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue", "mtime": 1753698968271}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_sanhao", "name", "props", "width", "type", "String", "default", "height", "title", "paramType", "required", "paramValue", "data", "chart", "loading", "sankeyData", "nodes", "links", "originalData", "expandedSubLabels", "Set", "resizeObserver", "resizeTimer", "date<PERSON><PERSON><PERSON>", "hasData", "baseHeight", "heightPerExpansion", "computed", "dynamicHeight", "enterpriseCount", "filter", "node", "category", "length", "calculatedHeight", "concat", "containerHeight", "mounted", "initDefaultDateRange", "initChart", "fetchSankeyData", "watch", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "disconnect", "clearTimeout", "window", "removeEventListener", "methods", "today", "Date", "sixMonthsAgo", "setMonth", "getMonth", "formatDate", "date", "year", "getFullYear", "month", "padStart", "day", "getDate", "onDateRangeChange", "updateDateRange", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "params", "response", "processedNodes", "processedLinks", "wrap", "_callee$", "_context", "prev", "next", "screenSn", "startDate", "endDate", "zcfxSankey", "sent", "console", "log", "processNodes", "processLinks", "filterEnterpriseNodes", "$nextTick", "updateChart", "t0", "error", "finish", "stop", "colors", "categoryToDepth", "党派", "专家", "提案", "父标签", "子标签", "企业", "map", "index", "depth", "undefined", "id", "itemStyle", "color", "_this2", "size", "filteredNodes", "filteredLinks", "link", "sourceExists", "find", "n", "source", "targetExists", "target", "connectedToSubLabel", "some", "sourceNode", "has", "targetNode", "value", "option", "getChartOption", "setOption", "backgroundColor", "text", "textStyle", "fontSize", "tooltip", "trigger", "triggerOn", "borderColor", "borderWidth", "formatter", "dataType", "depthMap", "levelText", "series", "layout", "emphasis", "focus", "nodeGap", "nodeWidth", "layoutIterations", "left", "right", "top", "bottom", "label", "show", "position", "substring", "lineStyle", "_this3", "$refs", "<PERSON><PERSON><PERSON><PERSON>", "style", "init", "on", "toggleEnterpriseNodes", "setupResizeListeners", "subLabelId", "delete", "add", "newSankeyData", "newHeight", "offsetHeight", "resize", "container", "parentElement", "scrollTop", "scrollTo", "behavior", "_this4", "ResizeObserver", "setTimeout", "observe", "parentContainer", "bind", "addEventListener", "_this5", "resizeChart"], "sources": ["src/views/bigScreenSanhao/components/sankeyChart2.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"bg-box\">\r\n      <div class=\"bg-box-title\">\r\n        <span>{{ title }}</span>\r\n        <div class=\"date-picker-container\">\r\n          <!-- 日期区间选择器 -->\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            @change=\"onDateRangeChange\"\r\n            size=\"small\"\r\n            class=\"title-date-picker\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bg-box-content\">\r\n        <div\r\n          class=\"sankey-container\"\r\n          :style=\"{ width: width, height: hasData ? containerHeight : '100px' }\"\r\n        >\r\n          <div\r\n            ref=\"sankeyChart\"\r\n            class=\"sankey-chart\"\r\n            :style=\"{ height: dynamicHeight }\"\r\n            v-show=\"hasData\"\r\n          ></div>\r\n          <div v-show=\"!hasData\" class=\"no-data-container\">\r\n            <div class=\"no-data-text\">暂无数据</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { zcfxSankey } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"SankeyChart2\",\r\n  props: {\r\n    width: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"关系图\",\r\n    },\r\n    // 接口参数类型：proposalsTitle、proposalsExperts、enterpriseName\r\n    paramType: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    // 参数值\r\n    paramValue: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null,\r\n      loading: false,\r\n      sankeyData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 原始数据，包含所有节点和连接\r\n      originalData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 记录哪些子标签节点已展开企业\r\n      expandedSubLabels: new Set(),\r\n      // ResizeObserver 实例\r\n      resizeObserver: null,\r\n      // 防抖定时器\r\n      resizeTimer: null,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 是否有数据\r\n      hasData: true,\r\n      // 基础高度\r\n      baseHeight: 600,\r\n      // 每个展开企业增加的高度\r\n      heightPerExpansion: 50,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算桑葚图高度\r\n    dynamicHeight() {\r\n      // 计算当前显示的企业节点数量\r\n      const enterpriseCount = this.sankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n\r\n      // 基础高度 + 企业数量 * 每个企业增加的高度\r\n      const calculatedHeight =\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion;\r\n      return `${calculatedHeight}px`;\r\n    },\r\n\r\n    // 动态计算容器高度（用于滚动）\r\n    containerHeight() {\r\n      // 容器高度固定为基础高度，超出部分显示滚动条\r\n      return `${this.baseHeight}px`;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.initDefaultDateRange();\r\n    this.initChart();\r\n    this.fetchSankeyData();\r\n  },\r\n\r\n  watch: {\r\n    // 监听宽度和高度变化\r\n    width() {\r\n      this.handleResize();\r\n    },\r\n    height() {\r\n      this.handleResize();\r\n    },\r\n    // 监听参数变化\r\n    paramValue() {\r\n      this.fetchSankeyData();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n    // 清理 ResizeObserver\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n    }\r\n    // 清理定时器\r\n    if (this.resizeTimer) {\r\n      clearTimeout(this.resizeTimer);\r\n    }\r\n    // 清理窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleResize);\r\n  },\r\n  methods: {\r\n    // 初始化默认日期范围（最近半年）\r\n    initDefaultDateRange() {\r\n      const today = new Date();\r\n      const sixMonthsAgo = new Date();\r\n      sixMonthsAgo.setMonth(today.getMonth() - 6);\r\n\r\n      this.dateRange = [this.formatDate(sixMonthsAgo), this.formatDate(today)];\r\n    },\r\n\r\n    // 格式化日期为 yyyy-MM-dd 格式\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n      const day = String(date.getDate()).padStart(2, \"0\");\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n    // 日期范围变化处理\r\n    onDateRangeChange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 更新日期范围（由父组件调用）\r\n    updateDateRange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 获取桑葚图数据\r\n    async fetchSankeyData() {\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 构建请求参数\r\n        const params = {\r\n          screenSn: \"1\",\r\n        };\r\n\r\n        // 根据参数类型添加对应的参数\r\n        if (this.paramType && this.paramValue) {\r\n          params[this.paramType] = this.paramValue;\r\n        }\r\n\r\n        // 如果有选择日期范围，添加日期参数\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.startDate = this.dateRange[0];\r\n          params.endDate = this.dateRange[1];\r\n        }\r\n\r\n        const response = await zcfxSankey(params);\r\n\r\n        if (response && response.data) {\r\n          console.log(\"桑葚图数据:\", response.data);\r\n          // 处理节点数据\r\n          const processedNodes = this.processNodes(response.data.nodes || []);\r\n          // 处理连接数据\r\n          const processedLinks = this.processLinks(response.data.links || []);\r\n\r\n          // 保存原始数据\r\n          this.originalData = {\r\n            nodes: processedNodes,\r\n            links: processedLinks,\r\n          };\r\n\r\n          // 初始化时隐藏企业节点\r\n          this.sankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n          if (this.sankeyData.links.length === 0) {\r\n            this.hasData = false;\r\n            this.sankeyData.nodes = [];\r\n          } else {\r\n            this.hasData = true;\r\n            // 如果有数据但图表还没初始化，则初始化图表\r\n            if (!this.chart) {\r\n              this.$nextTick(() => {\r\n                this.initChart();\r\n              });\r\n            }\r\n          }\r\n\r\n          // 更新图表\r\n          this.updateChart();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取桑葚图数据失败:\", error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理节点数据\r\n    processNodes(nodes) {\r\n      const colors = [\"#dd79ff\", \"#58d9f9\", \"#4992ff\"];\r\n\r\n      // category到层级的映射\r\n      const categoryToDepth = {\r\n        党派: 0,\r\n        专家: 1,\r\n        提案: 2,\r\n        父标签: 3,\r\n        子标签: 4,\r\n        企业: 5,\r\n      };\r\n\r\n      return nodes.map((node, index) => {\r\n        const depth =\r\n          categoryToDepth[node.category] !== undefined\r\n            ? categoryToDepth[node.category]\r\n            : 0;\r\n\r\n        return {\r\n          id: node.id,\r\n          name: node.name,\r\n          category: node.category,\r\n          depth: depth,\r\n          itemStyle: {\r\n            color: colors[index % colors.length],\r\n          },\r\n        };\r\n      });\r\n    },\r\n\r\n    // 过滤企业节点，根据展开状态决定是否显示\r\n    filterEnterpriseNodes(data) {\r\n      // 如果没有展开任何子标签，则隐藏所有企业节点\r\n      if (this.expandedSubLabels.size === 0) {\r\n        const filteredNodes = data.nodes.filter(\r\n          (node) => node.category !== \"企业\"\r\n        );\r\n        const filteredLinks = data.links.filter((link) => {\r\n          const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n          const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n          return sourceExists && targetExists;\r\n        });\r\n\r\n        return {\r\n          nodes: filteredNodes,\r\n          links: filteredLinks,\r\n        };\r\n      }\r\n\r\n      // 如果有展开的子标签，则显示对应的企业节点\r\n      const filteredNodes = data.nodes.filter((node) => {\r\n        if (node.category === \"企业\") {\r\n          // 查找连接到此企业的子标签节点\r\n          const connectedToSubLabel = data.links.some((link) => {\r\n            // 检查企业是否与已展开的子标签相连\r\n            if (link.target === node.id) {\r\n              // 企业是目标，检查源是否是已展开的子标签\r\n              const sourceNode = data.nodes.find((n) => n.id === link.source);\r\n              return (\r\n                sourceNode &&\r\n                sourceNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(sourceNode.id)\r\n              );\r\n            }\r\n            if (link.source === node.id) {\r\n              // 企业是源，检查目标是否是已展开的子标签\r\n              const targetNode = data.nodes.find((n) => n.id === link.target);\r\n              return (\r\n                targetNode &&\r\n                targetNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(targetNode.id)\r\n              );\r\n            }\r\n            return false;\r\n          });\r\n\r\n          return connectedToSubLabel;\r\n        }\r\n        return true; // 非企业节点都显示\r\n      });\r\n\r\n      const filteredLinks = data.links.filter((link) => {\r\n        const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n        const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n        return sourceExists && targetExists;\r\n      });\r\n\r\n      return {\r\n        nodes: filteredNodes,\r\n        links: filteredLinks,\r\n      };\r\n    },\r\n\r\n    // 处理连接数据\r\n    processLinks(links) {\r\n      return links.map((link) => {\r\n        return {\r\n          source: link.source,\r\n          target: link.target,\r\n          value: link.value || 1, // 如果没有value，默认为1\r\n        };\r\n      });\r\n    },\r\n\r\n    // 更新图表\r\n    updateChart() {\r\n      if (this.chart && this.hasData) {\r\n        const option = this.getChartOption();\r\n        // 使用 notMerge: false 来避免完全重新渲染，提高性能\r\n        this.chart.setOption(option, true);\r\n      }\r\n    },\r\n\r\n    // 获取图表配置\r\n    getChartOption() {\r\n      return {\r\n        backgroundColor: \"transparent\",\r\n        title: {\r\n          text: \"\",\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n            fontSize: 16,\r\n          },\r\n        },\r\n        tooltip: {\r\n          trigger: \"item\",\r\n          triggerOn: \"mousemove\",\r\n          backgroundColor: \"rgba(0, 0, 0, 0.8)\",\r\n          borderColor: \"#0ec2f4\",\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n          },\r\n          formatter: function (params) {\r\n            if (params.dataType === \"edge\") {\r\n              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;\r\n            } else {\r\n              const depthMap = {\r\n                0: \"第1级\",\r\n                1: \"第2级\",\r\n                2: \"第3级\",\r\n                3: \"第4级\",\r\n                4: \"第5级\",\r\n                5: \"第6级\",\r\n              };\r\n              const levelText = depthMap[params.data.depth] || \"未知层级\";\r\n              return `${params.data.name}<br/>类别: ${\r\n                params.data.category || \"未知\"\r\n              }<br/>层级: ${levelText}`;\r\n            }\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            type: \"sankey\",\r\n            layout: \"none\",\r\n            emphasis: {\r\n              focus: \"adjacency\",\r\n            },\r\n            data: this.sankeyData.nodes,\r\n            links: this.sankeyData.links,\r\n            // orient: 'vertical',\r\n            // nodeAlign: 'justify',\r\n            nodeGap: 10,\r\n            nodeWidth: 40,\r\n            layoutIterations: 0,\r\n            left: \"0\",\r\n            right: \"20%\",\r\n            top: \"0.2%\",\r\n            bottom: \"0.2%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#ffffff\",\r\n              fontSize: 14,\r\n              formatter: function (params) {\r\n                return params.name.length > 12\r\n                  ? params.name.substring(0, 12) + \"...\"\r\n                  : params.name;\r\n              },\r\n            },\r\n            lineStyle: {\r\n              color: \"source\",\r\n              // curveness: 0.2,\r\n              // opacity: 0.7\r\n            },\r\n            // itemStyle: {\r\n            //   borderWidth: 1,\r\n            //   borderColor: '#0ec2f4'\r\n            // }\r\n          },\r\n        ],\r\n      };\r\n    },\r\n\r\n    initChart() {\r\n      if (!this.hasData) {\r\n        return; // 没有数据时不初始化图表\r\n      }\r\n\r\n      // 设置图表容器的尺寸 - 初始化时使用基础高度\r\n      this.$refs.sankeyChart.style.width = \"100%\";\r\n      this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n\r\n      this.chart = echarts.init(this.$refs.sankeyChart);\r\n\r\n      // 初始化时设置空的图表配置\r\n      const option = this.getChartOption();\r\n      this.chart.setOption(option);\r\n\r\n      // 添加节点点击事件\r\n      this.chart.on(\"click\", (params) => {\r\n        if (params.dataType === \"node\" && params.data.category === \"子标签\") {\r\n          this.toggleEnterpriseNodes(params.data.id);\r\n        }\r\n      });\r\n\r\n      // 设置尺寸变化监听\r\n      this.setupResizeListeners();\r\n    },\r\n\r\n    // 切换企业节点的显示/隐藏\r\n    toggleEnterpriseNodes(subLabelId) {\r\n      if (this.expandedSubLabels.has(subLabelId)) {\r\n        // 如果已展开，则收起\r\n        this.expandedSubLabels.delete(subLabelId);\r\n      } else {\r\n        // 如果未展开，则展开\r\n        this.expandedSubLabels.add(subLabelId);\r\n      }\r\n\r\n      // 重新过滤数据\r\n      const newSankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n      // 计算新高度\r\n      const enterpriseCount = newSankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n      const newHeight = `${\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion\r\n      }px`;\r\n\r\n      // 同时更新数据、高度和图表，确保完全同步\r\n      this.sankeyData = newSankeyData;\r\n      if (this.$refs.sankeyChart) {\r\n        this.$refs.sankeyChart.style.height = newHeight;\r\n      }\r\n\r\n      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度\r\n      if (this.expandedSubLabels.size === 0) {\r\n        // 先强制设置高度为基础高度\r\n        if (this.$refs.sankeyChart) {\r\n          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n          // 强制浏览器重新计算布局\r\n          this.$refs.sankeyChart.offsetHeight;\r\n        }\r\n\r\n        // 立即更新图表以确保高度生效\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n\r\n        // 然后滚动到顶部\r\n        const container = this.$refs.sankeyChart.parentElement;\r\n        if (container) {\r\n          // 强制触发重绘\r\n          container.offsetHeight;\r\n          container.scrollTop = 0;\r\n          // 使用 scrollTo 确保滚动生效\r\n          container.scrollTo({ top: 0, behavior: \"instant\" });\r\n        }\r\n      }\r\n\r\n      // 立即更新图表\r\n      this.updateChart();\r\n      if (this.chart) {\r\n        this.chart.resize();\r\n      }\r\n    },\r\n\r\n    // 设置尺寸变化监听\r\n    setupResizeListeners() {\r\n      // 创建 ResizeObserver 监听容器尺寸变化\r\n      if (window.ResizeObserver) {\r\n        this.resizeObserver = new ResizeObserver(() => {\r\n          // 使用防抖处理，避免频繁触发\r\n          clearTimeout(this.resizeTimer);\r\n          this.resizeTimer = setTimeout(() => {\r\n            this.handleResize();\r\n          }, 100);\r\n        });\r\n\r\n        // 监听图表容器的尺寸变化\r\n        this.resizeObserver.observe(this.$refs.sankeyChart);\r\n\r\n        // 也监听父容器的尺寸变化\r\n        const parentContainer = this.$refs.sankeyChart.parentElement;\r\n        if (parentContainer) {\r\n          this.resizeObserver.observe(parentContainer);\r\n        }\r\n      }\r\n\r\n      // 监听窗口大小变化（作为备用方案）\r\n      this.handleResize = this.handleResize.bind(this);\r\n      window.addEventListener(\"resize\", this.handleResize);\r\n    },\r\n\r\n    // 处理尺寸变化\r\n    handleResize() {\r\n      if (this.chart) {\r\n        // 延迟执行 resize，确保 DOM 更新完成\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    // 手动触发图表重新调整大小（供父组件调用）\r\n    resizeChart() {\r\n      this.handleResize();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bg-box {\r\n  position: relative;\r\n  background: #1b283b;\r\n  border-radius: 8px;\r\n  padding: 8px 16px 16px;\r\n  margin-bottom: 20px;\r\n\r\n  .bg-box-title {\r\n    font-weight: 800;\r\n    font-size: 18px;\r\n    color: #ffffff;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .bg-box-content {\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    white-space: pre-wrap;\r\n  }\r\n}\r\n\r\n.date-picker-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.title-date-picker {\r\n  width: 240px;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner {\r\n  background: rgba(27, 40, 59, 0.8) !important;\r\n  border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  color: #ffffff !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-separator {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input {\r\n  background: transparent !important;\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n// 额外的强制样式覆盖\r\n::v-deep .el-date-editor.el-input {\r\n  .el-input__wrapper {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  }\r\n\r\n  .el-input__inner {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n    color: #ffffff !important;\r\n  }\r\n}\r\n\r\n.sankey-container {\r\n  overflow-y: auto; /* 允许垂直滚动 */\r\n  overflow-x: hidden;\r\n  position: relative;\r\n}\r\n\r\n.sankey-chart {\r\n  width: 100%;\r\n  min-height: 600px; /* 设置最小高度，确保图表有足够空间渲染 */\r\n  /* 完全移除过渡动画，实现瞬间变化 */\r\n}\r\n\r\n.no-data-container {\r\n  width: 100%;\r\n  height: 100px; /* 无数据时的较小高度 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.no-data-text {\r\n  color: #fff;\r\n  font-size: 16px;\r\n  text-align: center;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.sankey-container::-webkit-scrollbar,\r\n.sankey-fullscreen-container::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-track,\r\n.sankey-fullscreen-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb:hover,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,KAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACA;IACAG,SAAA;MACAL,IAAA,EAAAC,MAAA;MACAK,QAAA;IACA;IACA;IACAC,UAAA;MACAP,IAAA,EAAAC,MAAA;MACAK,QAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,UAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACA;MACAC,YAAA;QACAF,KAAA;QACAC,KAAA;MACA;MACA;MACAE,iBAAA,MAAAC,GAAA;MACA;MACAC,cAAA;MACA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA;MACA,IAAAC,eAAA,QAAAd,UAAA,CAAAC,KAAA,CAAAc,MAAA,CACA,UAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,CACA,EAAAC,MAAA;;MAEA;MACA,IAAAC,gBAAA,GACA,KAAAT,UAAA,GAAAI,eAAA,QAAAH,kBAAA;MACA,UAAAS,MAAA,CAAAD,gBAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAA;MACA;MACA,UAAAD,MAAA,MAAAV,UAAA;IACA;EACA;EACAY,OAAA,WAAAA,QAAA;IACA,KAAAC,oBAAA;IACA,KAAAC,SAAA;IACA,KAAAC,eAAA;EACA;EAEAC,KAAA;IACA;IACAtC,KAAA,WAAAA,MAAA;MACA,KAAAuC,YAAA;IACA;IACAnC,MAAA,WAAAA,OAAA;MACA,KAAAmC,YAAA;IACA;IACA;IACA/B,UAAA,WAAAA,WAAA;MACA,KAAA6B,eAAA;IACA;EACA;EACAG,aAAA,WAAAA,cAAA;IACA,SAAA9B,KAAA;MACA,KAAAA,KAAA,CAAA+B,OAAA;IACA;IACA;IACA,SAAAvB,cAAA;MACA,KAAAA,cAAA,CAAAwB,UAAA;IACA;IACA;IACA,SAAAvB,WAAA;MACAwB,YAAA,MAAAxB,WAAA;IACA;IACA;IACAyB,MAAA,CAAAC,mBAAA,gBAAAN,YAAA;EACA;EACAO,OAAA;IACA;IACAX,oBAAA,WAAAA,qBAAA;MACA,IAAAY,KAAA,OAAAC,IAAA;MACA,IAAAC,YAAA,OAAAD,IAAA;MACAC,YAAA,CAAAC,QAAA,CAAAH,KAAA,CAAAI,QAAA;MAEA,KAAA/B,SAAA,SAAAgC,UAAA,CAAAH,YAAA,QAAAG,UAAA,CAAAL,KAAA;IACA;IAEA;IACAK,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,IAAA,GAAAD,IAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAAtD,MAAA,CAAAmD,IAAA,CAAAF,QAAA,QAAAM,QAAA;MACA,IAAAC,GAAA,GAAAxD,MAAA,CAAAmD,IAAA,CAAAM,OAAA,IAAAF,QAAA;MACA,UAAAzB,MAAA,CAAAsB,IAAA,OAAAtB,MAAA,CAAAwB,KAAA,OAAAxB,MAAA,CAAA0B,GAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAA;MACA,KAAAvB,eAAA;IACA;IAEA;IACAwB,eAAA,WAAAA,gBAAA;MACA,KAAAxB,eAAA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAyB,KAAA;MAAA,WAAAC,kBAAA,CAAA5D,OAAA,mBAAA6D,oBAAA,CAAA7D,OAAA,IAAA8D,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,cAAA;QAAA,WAAAN,oBAAA,CAAA7D,OAAA,IAAAoE,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAnD,OAAA;;cAEA;cACAwD,MAAA;gBACAS,QAAA;cACA,GAEA;cACA,IAAAd,KAAA,CAAAxD,SAAA,IAAAwD,KAAA,CAAAtD,UAAA;gBACA2D,MAAA,CAAAL,KAAA,CAAAxD,SAAA,IAAAwD,KAAA,CAAAtD,UAAA;cACA;;cAEA;cACA,IAAAsD,KAAA,CAAA1C,SAAA,IAAA0C,KAAA,CAAA1C,SAAA,CAAAU,MAAA;gBACAqC,MAAA,CAAAU,SAAA,GAAAf,KAAA,CAAA1C,SAAA;gBACA+C,MAAA,CAAAW,OAAA,GAAAhB,KAAA,CAAA1C,SAAA;cACA;cAAAqD,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAI,kBAAA,EAAAZ,MAAA;YAAA;cAAAC,QAAA,GAAAK,QAAA,CAAAO,IAAA;cAEA,IAAAZ,QAAA,IAAAA,QAAA,CAAA3D,IAAA;gBACAwE,OAAA,CAAAC,GAAA,gQAAAd,QAAA,CAAA3D,IAAA;gBACA;gBACA4D,cAAA,GAAAP,KAAA,CAAAqB,YAAA,CAAAf,QAAA,CAAA3D,IAAA,CAAAI,KAAA,SACA;gBACAyD,cAAA,GAAAR,KAAA,CAAAsB,YAAA,CAAAhB,QAAA,CAAA3D,IAAA,CAAAK,KAAA,SAEA;gBACAgD,KAAA,CAAA/C,YAAA;kBACAF,KAAA,EAAAwD,cAAA;kBACAvD,KAAA,EAAAwD;gBACA;;gBAEA;gBACAR,KAAA,CAAAlD,UAAA,GAAAkD,KAAA,CAAAuB,qBAAA,CAAAvB,KAAA,CAAA/C,YAAA;gBAEA,IAAA+C,KAAA,CAAAlD,UAAA,CAAAE,KAAA,CAAAgB,MAAA;kBACAgC,KAAA,CAAAzC,OAAA;kBACAyC,KAAA,CAAAlD,UAAA,CAAAC,KAAA;gBACA;kBACAiD,KAAA,CAAAzC,OAAA;kBACA;kBACA,KAAAyC,KAAA,CAAApD,KAAA;oBACAoD,KAAA,CAAAwB,SAAA;sBACAxB,KAAA,CAAA1B,SAAA;oBACA;kBACA;gBACA;;gBAEA;gBACA0B,KAAA,CAAAyB,WAAA;cACA;cAAAd,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAe,EAAA,GAAAf,QAAA;cAEAQ,OAAA,CAAAQ,KAAA,eAAAhB,QAAA,CAAAe,EAAA;YAAA;cAAAf,QAAA,CAAAC,IAAA;cAEAZ,KAAA,CAAAnD,OAAA;cAAA,OAAA8D,QAAA,CAAAiB,MAAA;YAAA;YAAA;cAAA,OAAAjB,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAAzB,OAAA;MAAA;IAEA;IAEA;IACAiB,YAAA,WAAAA,aAAAtE,KAAA;MACA,IAAA+E,MAAA;;MAEA;MACA,IAAAC,eAAA;QACAC,EAAA;QACAC,EAAA;QACAC,EAAA;QACAC,GAAA;QACAC,GAAA;QACAC,EAAA;MACA;MAEA,OAAAtF,KAAA,CAAAuF,GAAA,WAAAxE,IAAA,EAAAyE,KAAA;QACA,IAAAC,KAAA,GACAT,eAAA,CAAAjE,IAAA,CAAAC,QAAA,MAAA0E,SAAA,GACAV,eAAA,CAAAjE,IAAA,CAAAC,QAAA,IACA;QAEA;UACA2E,EAAA,EAAA5E,IAAA,CAAA4E,EAAA;UACA1G,IAAA,EAAA8B,IAAA,CAAA9B,IAAA;UACA+B,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAyE,KAAA,EAAAA,KAAA;UACAG,SAAA;YACAC,KAAA,EAAAd,MAAA,CAAAS,KAAA,GAAAT,MAAA,CAAA9D,MAAA;UACA;QACA;MACA;IACA;IAEA;IACAuD,qBAAA,WAAAA,sBAAA5E,IAAA;MAAA,IAAAkG,MAAA;MACA;MACA,SAAA3F,iBAAA,CAAA4F,IAAA;QACA,IAAAC,cAAA,GAAApG,IAAA,CAAAI,KAAA,CAAAc,MAAA,CACA,UAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,QAAA;QAAA,CACA;QACA,IAAAiF,cAAA,GAAArG,IAAA,CAAAK,KAAA,CAAAa,MAAA,WAAAoF,IAAA;UACA,IAAAC,YAAA,GAAAH,cAAA,CAAAI,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAI,MAAA;UAAA;UACA,IAAAC,YAAA,GAAAP,cAAA,CAAAI,IAAA,WAAAC,CAAA;YAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAM,MAAA;UAAA;UACA,OAAAL,YAAA,IAAAI,YAAA;QACA;QAEA;UACAvG,KAAA,EAAAgG,cAAA;UACA/F,KAAA,EAAAgG;QACA;MACA;;MAEA;MACA,IAAAD,aAAA,GAAApG,IAAA,CAAAI,KAAA,CAAAc,MAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,QAAA;UACA;UACA,IAAAyF,mBAAA,GAAA7G,IAAA,CAAAK,KAAA,CAAAyG,IAAA,WAAAR,IAAA;YACA;YACA,IAAAA,IAAA,CAAAM,MAAA,KAAAzF,IAAA,CAAA4E,EAAA;cACA;cACA,IAAAgB,UAAA,GAAA/G,IAAA,CAAAI,KAAA,CAAAoG,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAI,MAAA;cAAA;cACA,OACAK,UAAA,IACAA,UAAA,CAAA3F,QAAA,cACA8E,MAAA,CAAA3F,iBAAA,CAAAyG,GAAA,CAAAD,UAAA,CAAAhB,EAAA;YAEA;YACA,IAAAO,IAAA,CAAAI,MAAA,KAAAvF,IAAA,CAAA4E,EAAA;cACA;cACA,IAAAkB,UAAA,GAAAjH,IAAA,CAAAI,KAAA,CAAAoG,IAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAM,MAAA;cAAA;cACA,OACAK,UAAA,IACAA,UAAA,CAAA7F,QAAA,cACA8E,MAAA,CAAA3F,iBAAA,CAAAyG,GAAA,CAAAC,UAAA,CAAAlB,EAAA;YAEA;YACA;UACA;UAEA,OAAAc,mBAAA;QACA;QACA;MACA;MAEA,IAAAR,aAAA,GAAArG,IAAA,CAAAK,KAAA,CAAAa,MAAA,WAAAoF,IAAA;QACA,IAAAC,YAAA,GAAAH,aAAA,CAAAI,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAI,MAAA;QAAA;QACA,IAAAC,YAAA,GAAAP,aAAA,CAAAI,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAV,EAAA,KAAAO,IAAA,CAAAM,MAAA;QAAA;QACA,OAAAL,YAAA,IAAAI,YAAA;MACA;MAEA;QACAvG,KAAA,EAAAgG,aAAA;QACA/F,KAAA,EAAAgG;MACA;IACA;IAEA;IACA1B,YAAA,WAAAA,aAAAtE,KAAA;MACA,OAAAA,KAAA,CAAAsF,GAAA,WAAAW,IAAA;QACA;UACAI,MAAA,EAAAJ,IAAA,CAAAI,MAAA;UACAE,MAAA,EAAAN,IAAA,CAAAM,MAAA;UACAM,KAAA,EAAAZ,IAAA,CAAAY,KAAA;QACA;MACA;IACA;IAEA;IACApC,WAAA,WAAAA,YAAA;MACA,SAAA7E,KAAA,SAAAW,OAAA;QACA,IAAAuG,MAAA,QAAAC,cAAA;QACA;QACA,KAAAnH,KAAA,CAAAoH,SAAA,CAAAF,MAAA;MACA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAA;MACA;QACAE,eAAA;QACA1H,KAAA;UACA2H,IAAA;UACAC,SAAA;YACAvB,KAAA;YACAwB,QAAA;UACA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;UACAN,eAAA;UACAO,WAAA;UACAC,WAAA;UACAN,SAAA;YACAvB,KAAA;UACA;UACA8B,SAAA,WAAAA,UAAArE,MAAA;YACA,IAAAA,MAAA,CAAAsE,QAAA;cACA,UAAAzG,MAAA,CAAAmC,MAAA,CAAA1D,IAAA,CAAA0G,MAAA,cAAAnF,MAAA,CAAAmC,MAAA,CAAA1D,IAAA,CAAA4G,MAAA,+BAAArF,MAAA,CAAAmC,MAAA,CAAA1D,IAAA,CAAAkH,KAAA;YACA;cACA,IAAAe,QAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cACA;cACA,IAAAC,SAAA,GAAAD,QAAA,CAAAvE,MAAA,CAAA1D,IAAA,CAAA6F,KAAA;cACA,UAAAtE,MAAA,CAAAmC,MAAA,CAAA1D,IAAA,CAAAX,IAAA,yBAAAkC,MAAA,CACAmC,MAAA,CAAA1D,IAAA,CAAAoB,QAAA,iCAAAG,MAAA,CACA2G,SAAA;YACA;UACA;QACA;QACAC,MAAA,GACA;UACA3I,IAAA;UACA4I,MAAA;UACAC,QAAA;YACAC,KAAA;UACA;UACAtI,IAAA,OAAAG,UAAA,CAAAC,KAAA;UACAC,KAAA,OAAAF,UAAA,CAAAE,KAAA;UACA;UACA;UACAkI,OAAA;UACAC,SAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;UACAC,MAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;YACA/C,KAAA;YACAwB,QAAA;YACAM,SAAA,WAAAA,UAAArE,MAAA;cACA,OAAAA,MAAA,CAAArE,IAAA,CAAAgC,MAAA,QACAqC,MAAA,CAAArE,IAAA,CAAA4J,SAAA,kBACAvF,MAAA,CAAArE,IAAA;YACA;UACA;UACA6J,SAAA;YACAjD,KAAA;YACA;YACA;UACA;UACA;UACA;UACA;UACA;QACA;MAEA;IACA;IAEAtE,SAAA,WAAAA,UAAA;MAAA,IAAAwH,MAAA;MACA,UAAAvI,OAAA;QACA;MACA;;MAEA;MACA,KAAAwI,KAAA,CAAAC,WAAA,CAAAC,KAAA,CAAA/J,KAAA;MACA,KAAA6J,KAAA,CAAAC,WAAA,CAAAC,KAAA,CAAA3J,MAAA,MAAA4B,MAAA,MAAAV,UAAA;MAEA,KAAAZ,KAAA,GAAAhB,OAAA,CAAAsK,IAAA,MAAAH,KAAA,CAAAC,WAAA;;MAEA;MACA,IAAAlC,MAAA,QAAAC,cAAA;MACA,KAAAnH,KAAA,CAAAoH,SAAA,CAAAF,MAAA;;MAEA;MACA,KAAAlH,KAAA,CAAAuJ,EAAA,oBAAA9F,MAAA;QACA,IAAAA,MAAA,CAAAsE,QAAA,eAAAtE,MAAA,CAAA1D,IAAA,CAAAoB,QAAA;UACA+H,MAAA,CAAAM,qBAAA,CAAA/F,MAAA,CAAA1D,IAAA,CAAA+F,EAAA;QACA;MACA;;MAEA;MACA,KAAA2D,oBAAA;IACA;IAEA;IACAD,qBAAA,WAAAA,sBAAAE,UAAA;MACA,SAAApJ,iBAAA,CAAAyG,GAAA,CAAA2C,UAAA;QACA;QACA,KAAApJ,iBAAA,CAAAqJ,MAAA,CAAAD,UAAA;MACA;QACA;QACA,KAAApJ,iBAAA,CAAAsJ,GAAA,CAAAF,UAAA;MACA;;MAEA;MACA,IAAAG,aAAA,QAAAlF,qBAAA,MAAAtE,YAAA;;MAEA;MACA,IAAAW,eAAA,GAAA6I,aAAA,CAAA1J,KAAA,CAAAc,MAAA,CACA,UAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,QAAA;MAAA,CACA,EAAAC,MAAA;MACA,IAAA0I,SAAA,MAAAxI,MAAA,CACA,KAAAV,UAAA,GAAAI,eAAA,QAAAH,kBAAA,OACA;;MAEA;MACA,KAAAX,UAAA,GAAA2J,aAAA;MACA,SAAAV,KAAA,CAAAC,WAAA;QACA,KAAAD,KAAA,CAAAC,WAAA,CAAAC,KAAA,CAAA3J,MAAA,GAAAoK,SAAA;MACA;;MAEA;MACA,SAAAxJ,iBAAA,CAAA4F,IAAA;QACA;QACA,SAAAiD,KAAA,CAAAC,WAAA;UACA,KAAAD,KAAA,CAAAC,WAAA,CAAAC,KAAA,CAAA3J,MAAA,MAAA4B,MAAA,MAAAV,UAAA;UACA;UACA,KAAAuI,KAAA,CAAAC,WAAA,CAAAW,YAAA;QACA;;QAEA;QACA,SAAA/J,KAAA;UACA,KAAAA,KAAA,CAAAgK,MAAA;QACA;;QAEA;QACA,IAAAC,SAAA,QAAAd,KAAA,CAAAC,WAAA,CAAAc,aAAA;QACA,IAAAD,SAAA;UACA;UACAA,SAAA,CAAAF,YAAA;UACAE,SAAA,CAAAE,SAAA;UACA;UACAF,SAAA,CAAAG,QAAA;YAAAzB,GAAA;YAAA0B,QAAA;UAAA;QACA;MACA;;MAEA;MACA,KAAAxF,WAAA;MACA,SAAA7E,KAAA;QACA,KAAAA,KAAA,CAAAgK,MAAA;MACA;IACA;IAEA;IACAP,oBAAA,WAAAA,qBAAA;MAAA,IAAAa,MAAA;MACA;MACA,IAAApI,MAAA,CAAAqI,cAAA;QACA,KAAA/J,cAAA,OAAA+J,cAAA;UACA;UACAtI,YAAA,CAAAqI,MAAA,CAAA7J,WAAA;UACA6J,MAAA,CAAA7J,WAAA,GAAA+J,UAAA;YACAF,MAAA,CAAAzI,YAAA;UACA;QACA;;QAEA;QACA,KAAArB,cAAA,CAAAiK,OAAA,MAAAtB,KAAA,CAAAC,WAAA;;QAEA;QACA,IAAAsB,eAAA,QAAAvB,KAAA,CAAAC,WAAA,CAAAc,aAAA;QACA,IAAAQ,eAAA;UACA,KAAAlK,cAAA,CAAAiK,OAAA,CAAAC,eAAA;QACA;MACA;;MAEA;MACA,KAAA7I,YAAA,QAAAA,YAAA,CAAA8I,IAAA;MACAzI,MAAA,CAAA0I,gBAAA,gBAAA/I,YAAA;IACA;IAEA;IACAA,YAAA,WAAAA,aAAA;MAAA,IAAAgJ,MAAA;MACA,SAAA7K,KAAA;QACA;QACA,KAAA4E,SAAA;UACAiG,MAAA,CAAA7K,KAAA,CAAAgK,MAAA;QACA;MACA;IACA;IAEA;IACAc,WAAA,WAAAA,YAAA;MACA,KAAAjJ,YAAA;IACA;EACA;AACA", "ignoreList": []}]}