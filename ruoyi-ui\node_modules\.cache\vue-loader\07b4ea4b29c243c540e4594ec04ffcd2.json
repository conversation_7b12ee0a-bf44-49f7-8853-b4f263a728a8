{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue?vue&type=style&index=0&id=05d78d16&lang=scss&scoped=true", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\bigScreenSanhao\\components\\sankeyChart2.vue", "mtime": 1753698968271}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmJnLWJveCB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgYmFja2dyb3VuZDogIzFiMjgzYjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiA4cHggMTZweCAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KDQogIC5iZy1ib3gtdGl0bGUgew0KICAgIGZvbnQtd2VpZ2h0OiA4MDA7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgIGhlaWdodDogMzBweDsNCiAgICBsaW5lLWhlaWdodDogMzBweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIH0NCg0KICAuYmctYm94LWNvbnRlbnQgew0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7DQogIH0NCn0NCg0KLmRhdGUtcGlja2VyLWNvbnRhaW5lciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTBweDsNCn0NCg0KLnRpdGxlLWRhdGUtcGlja2VyIHsNCiAgd2lkdGg6IDI0MHB4Ow0KfQ0KDQouZGF0ZS1waWNrZXItY29udGFpbmVyIDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI3LCA0MCwgNTksIDAuOCkgIWltcG9ydGFudDsNCiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNCwgMTk0LCAyNDQsIDAuMykgIWltcG9ydGFudDsNCiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCiAgZm9udC1zaXplOiAxMnB4ICFpbXBvcnRhbnQ7DQp9DQoNCi5kYXRlLXBpY2tlci1jb250YWluZXIgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lcjo6cGxhY2Vob2xkZXIgew0KICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjYpICFpbXBvcnRhbnQ7DQp9DQoNCi5kYXRlLXBpY2tlci1jb250YWluZXIgOjp2LWRlZXAgLmVsLXJhbmdlLXNlcGFyYXRvciB7DQogIGNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7DQp9DQoNCi5kYXRlLXBpY2tlci1jb250YWluZXIgOjp2LWRlZXAgLmVsLXJhbmdlLWlucHV0IHsNCiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsNCiAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCn0NCg0KLmRhdGUtcGlja2VyLWNvbnRhaW5lciA6OnYtZGVlcCAuZWwtcmFuZ2UtaW5wdXQ6OnBsYWNlaG9sZGVyIHsNCiAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC42KSAhaW1wb3J0YW50Ow0KfQ0KDQovLyDpop3lpJbnmoTlvLrliLbmoLflvI/opobnm5YNCjo6di1kZWVwIC5lbC1kYXRlLWVkaXRvci5lbC1pbnB1dCB7DQogIC5lbC1pbnB1dF9fd3JhcHBlciB7DQogICAgYmFja2dyb3VuZDogcmdiYSgyNywgNDAsIDU5LCAwLjgpICFpbXBvcnRhbnQ7DQogICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgxNCwgMTk0LCAyNDQsIDAuMykgIWltcG9ydGFudDsNCiAgfQ0KDQogIC5lbC1pbnB1dF9faW5uZXIgew0KICAgIGJhY2tncm91bmQ6IHJnYmEoMjcsIDQwLCA1OSwgMC44KSAhaW1wb3J0YW50Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMTQsIDE5NCwgMjQ0LCAwLjMpICFpbXBvcnRhbnQ7DQogICAgY29sb3I6ICNmZmZmZmYgIWltcG9ydGFudDsNCiAgfQ0KfQ0KDQouc2Fua2V5LWNvbnRhaW5lciB7DQogIG92ZXJmbG93LXk6IGF1dG87IC8qIOWFgeiuuOWeguebtOa7muWKqCAqLw0KICBvdmVyZmxvdy14OiBoaWRkZW47DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLnNhbmtleS1jaGFydCB7DQogIHdpZHRoOiAxMDAlOw0KICBtaW4taGVpZ2h0OiA2MDBweDsgLyog6K6+572u5pyA5bCP6auY5bqm77yM56Gu5L+d5Zu+6KGo5pyJ6Laz5aSf56m66Ze05riy5p+TICovDQogIC8qIOWujOWFqOenu+mZpOi/h+a4oeWKqOeUu++8jOWunueOsOeerOmXtOWPmOWMliAqLw0KfQ0KDQoubm8tZGF0YS1jb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDBweDsgLyog5peg5pWw5o2u5pe255qE6L6D5bCP6auY5bqmICovDQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KfQ0KDQoubm8tZGF0YS10ZXh0IHsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQovKiDoh6rlrprkuYnmu5rliqjmnaHmoLflvI8gKi8NCi5zYW5rZXktY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhciwNCi5zYW5rZXktZnVsbHNjcmVlbi1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyIHsNCiAgd2lkdGg6IDhweDsNCn0NCg0KLnNhbmtleS1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrLA0KLnNhbmtleS1mdWxsc2NyZWVuLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLnNhbmtleS1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iLA0KLnNhbmtleS1mdWxsc2NyZWVuLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDE0LCAxOTQsIDI0NCwgMC42KTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouc2Fua2V5LWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6aG92ZXIsDQouc2Fua2V5LWZ1bGxzY3JlZW4tY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7DQogIGJhY2tncm91bmQ6IHJnYmEoMTQsIDE5NCwgMjQ0LCAwLjgpOw0KfQ0K"}, {"version": 3, "sources": ["sankeyChart2.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4jBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "sankeyChart2.vue", "sourceRoot": "src/views/bigScreenSanhao/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"bg-box\">\r\n      <div class=\"bg-box-title\">\r\n        <span>{{ title }}</span>\r\n        <div class=\"date-picker-container\">\r\n          <!-- 日期区间选择器 -->\r\n          <el-date-picker\r\n            v-model=\"dateRange\"\r\n            type=\"daterange\"\r\n            range-separator=\"至\"\r\n            start-placeholder=\"开始日期\"\r\n            end-placeholder=\"结束日期\"\r\n            format=\"yyyy-MM-dd\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            @change=\"onDateRangeChange\"\r\n            size=\"small\"\r\n            class=\"title-date-picker\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div class=\"bg-box-content\">\r\n        <div\r\n          class=\"sankey-container\"\r\n          :style=\"{ width: width, height: hasData ? containerHeight : '100px' }\"\r\n        >\r\n          <div\r\n            ref=\"sankeyChart\"\r\n            class=\"sankey-chart\"\r\n            :style=\"{ height: dynamicHeight }\"\r\n            v-show=\"hasData\"\r\n          ></div>\r\n          <div v-show=\"!hasData\" class=\"no-data-container\">\r\n            <div class=\"no-data-text\">暂无数据</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { zcfxSankey } from \"@/api/bigScreen/sanhao.js\";\r\n\r\nexport default {\r\n  name: \"SankeyChart2\",\r\n  props: {\r\n    width: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: \"100%\",\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"关系图\",\r\n    },\r\n    // 接口参数类型：proposalsTitle、proposalsExperts、enterpriseName\r\n    paramType: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n    // 参数值\r\n    paramValue: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null,\r\n      loading: false,\r\n      sankeyData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 原始数据，包含所有节点和连接\r\n      originalData: {\r\n        nodes: [],\r\n        links: [],\r\n      },\r\n      // 记录哪些子标签节点已展开企业\r\n      expandedSubLabels: new Set(),\r\n      // ResizeObserver 实例\r\n      resizeObserver: null,\r\n      // 防抖定时器\r\n      resizeTimer: null,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 是否有数据\r\n      hasData: true,\r\n      // 基础高度\r\n      baseHeight: 600,\r\n      // 每个展开企业增加的高度\r\n      heightPerExpansion: 50,\r\n    };\r\n  },\r\n  computed: {\r\n    // 动态计算桑葚图高度\r\n    dynamicHeight() {\r\n      // 计算当前显示的企业节点数量\r\n      const enterpriseCount = this.sankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n\r\n      // 基础高度 + 企业数量 * 每个企业增加的高度\r\n      const calculatedHeight =\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion;\r\n      return `${calculatedHeight}px`;\r\n    },\r\n\r\n    // 动态计算容器高度（用于滚动）\r\n    containerHeight() {\r\n      // 容器高度固定为基础高度，超出部分显示滚动条\r\n      return `${this.baseHeight}px`;\r\n    },\r\n  },\r\n  mounted() {\r\n    this.initDefaultDateRange();\r\n    this.initChart();\r\n    this.fetchSankeyData();\r\n  },\r\n\r\n  watch: {\r\n    // 监听宽度和高度变化\r\n    width() {\r\n      this.handleResize();\r\n    },\r\n    height() {\r\n      this.handleResize();\r\n    },\r\n    // 监听参数变化\r\n    paramValue() {\r\n      this.fetchSankeyData();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    if (this.chart) {\r\n      this.chart.dispose();\r\n    }\r\n    // 清理 ResizeObserver\r\n    if (this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n    }\r\n    // 清理定时器\r\n    if (this.resizeTimer) {\r\n      clearTimeout(this.resizeTimer);\r\n    }\r\n    // 清理窗口大小变化监听\r\n    window.removeEventListener(\"resize\", this.handleResize);\r\n  },\r\n  methods: {\r\n    // 初始化默认日期范围（最近半年）\r\n    initDefaultDateRange() {\r\n      const today = new Date();\r\n      const sixMonthsAgo = new Date();\r\n      sixMonthsAgo.setMonth(today.getMonth() - 6);\r\n\r\n      this.dateRange = [this.formatDate(sixMonthsAgo), this.formatDate(today)];\r\n    },\r\n\r\n    // 格式化日期为 yyyy-MM-dd 格式\r\n    formatDate(date) {\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n      const day = String(date.getDate()).padStart(2, \"0\");\r\n      return `${year}-${month}-${day}`;\r\n    },\r\n\r\n    // 日期范围变化处理\r\n    onDateRangeChange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 更新日期范围（由父组件调用）\r\n    updateDateRange() {\r\n      this.fetchSankeyData();\r\n    },\r\n\r\n    // 获取桑葚图数据\r\n    async fetchSankeyData() {\r\n      try {\r\n        this.loading = true;\r\n\r\n        // 构建请求参数\r\n        const params = {\r\n          screenSn: \"1\",\r\n        };\r\n\r\n        // 根据参数类型添加对应的参数\r\n        if (this.paramType && this.paramValue) {\r\n          params[this.paramType] = this.paramValue;\r\n        }\r\n\r\n        // 如果有选择日期范围，添加日期参数\r\n        if (this.dateRange && this.dateRange.length === 2) {\r\n          params.startDate = this.dateRange[0];\r\n          params.endDate = this.dateRange[1];\r\n        }\r\n\r\n        const response = await zcfxSankey(params);\r\n\r\n        if (response && response.data) {\r\n          console.log(\"桑葚图数据:\", response.data);\r\n          // 处理节点数据\r\n          const processedNodes = this.processNodes(response.data.nodes || []);\r\n          // 处理连接数据\r\n          const processedLinks = this.processLinks(response.data.links || []);\r\n\r\n          // 保存原始数据\r\n          this.originalData = {\r\n            nodes: processedNodes,\r\n            links: processedLinks,\r\n          };\r\n\r\n          // 初始化时隐藏企业节点\r\n          this.sankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n          if (this.sankeyData.links.length === 0) {\r\n            this.hasData = false;\r\n            this.sankeyData.nodes = [];\r\n          } else {\r\n            this.hasData = true;\r\n            // 如果有数据但图表还没初始化，则初始化图表\r\n            if (!this.chart) {\r\n              this.$nextTick(() => {\r\n                this.initChart();\r\n              });\r\n            }\r\n          }\r\n\r\n          // 更新图表\r\n          this.updateChart();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取桑葚图数据失败:\", error);\r\n      } finally {\r\n        this.loading = false;\r\n      }\r\n    },\r\n\r\n    // 处理节点数据\r\n    processNodes(nodes) {\r\n      const colors = [\"#dd79ff\", \"#58d9f9\", \"#4992ff\"];\r\n\r\n      // category到层级的映射\r\n      const categoryToDepth = {\r\n        党派: 0,\r\n        专家: 1,\r\n        提案: 2,\r\n        父标签: 3,\r\n        子标签: 4,\r\n        企业: 5,\r\n      };\r\n\r\n      return nodes.map((node, index) => {\r\n        const depth =\r\n          categoryToDepth[node.category] !== undefined\r\n            ? categoryToDepth[node.category]\r\n            : 0;\r\n\r\n        return {\r\n          id: node.id,\r\n          name: node.name,\r\n          category: node.category,\r\n          depth: depth,\r\n          itemStyle: {\r\n            color: colors[index % colors.length],\r\n          },\r\n        };\r\n      });\r\n    },\r\n\r\n    // 过滤企业节点，根据展开状态决定是否显示\r\n    filterEnterpriseNodes(data) {\r\n      // 如果没有展开任何子标签，则隐藏所有企业节点\r\n      if (this.expandedSubLabels.size === 0) {\r\n        const filteredNodes = data.nodes.filter(\r\n          (node) => node.category !== \"企业\"\r\n        );\r\n        const filteredLinks = data.links.filter((link) => {\r\n          const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n          const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n          return sourceExists && targetExists;\r\n        });\r\n\r\n        return {\r\n          nodes: filteredNodes,\r\n          links: filteredLinks,\r\n        };\r\n      }\r\n\r\n      // 如果有展开的子标签，则显示对应的企业节点\r\n      const filteredNodes = data.nodes.filter((node) => {\r\n        if (node.category === \"企业\") {\r\n          // 查找连接到此企业的子标签节点\r\n          const connectedToSubLabel = data.links.some((link) => {\r\n            // 检查企业是否与已展开的子标签相连\r\n            if (link.target === node.id) {\r\n              // 企业是目标，检查源是否是已展开的子标签\r\n              const sourceNode = data.nodes.find((n) => n.id === link.source);\r\n              return (\r\n                sourceNode &&\r\n                sourceNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(sourceNode.id)\r\n              );\r\n            }\r\n            if (link.source === node.id) {\r\n              // 企业是源，检查目标是否是已展开的子标签\r\n              const targetNode = data.nodes.find((n) => n.id === link.target);\r\n              return (\r\n                targetNode &&\r\n                targetNode.category === \"子标签\" &&\r\n                this.expandedSubLabels.has(targetNode.id)\r\n              );\r\n            }\r\n            return false;\r\n          });\r\n\r\n          return connectedToSubLabel;\r\n        }\r\n        return true; // 非企业节点都显示\r\n      });\r\n\r\n      const filteredLinks = data.links.filter((link) => {\r\n        const sourceExists = filteredNodes.find((n) => n.id === link.source);\r\n        const targetExists = filteredNodes.find((n) => n.id === link.target);\r\n        return sourceExists && targetExists;\r\n      });\r\n\r\n      return {\r\n        nodes: filteredNodes,\r\n        links: filteredLinks,\r\n      };\r\n    },\r\n\r\n    // 处理连接数据\r\n    processLinks(links) {\r\n      return links.map((link) => {\r\n        return {\r\n          source: link.source,\r\n          target: link.target,\r\n          value: link.value || 1, // 如果没有value，默认为1\r\n        };\r\n      });\r\n    },\r\n\r\n    // 更新图表\r\n    updateChart() {\r\n      if (this.chart && this.hasData) {\r\n        const option = this.getChartOption();\r\n        // 使用 notMerge: false 来避免完全重新渲染，提高性能\r\n        this.chart.setOption(option, true);\r\n      }\r\n    },\r\n\r\n    // 获取图表配置\r\n    getChartOption() {\r\n      return {\r\n        backgroundColor: \"transparent\",\r\n        title: {\r\n          text: \"\",\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n            fontSize: 16,\r\n          },\r\n        },\r\n        tooltip: {\r\n          trigger: \"item\",\r\n          triggerOn: \"mousemove\",\r\n          backgroundColor: \"rgba(0, 0, 0, 0.8)\",\r\n          borderColor: \"#0ec2f4\",\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: \"#ffffff\",\r\n          },\r\n          formatter: function (params) {\r\n            if (params.dataType === \"edge\") {\r\n              return `${params.data.source} → ${params.data.target}<br/>影响度: ${params.data.value}`;\r\n            } else {\r\n              const depthMap = {\r\n                0: \"第1级\",\r\n                1: \"第2级\",\r\n                2: \"第3级\",\r\n                3: \"第4级\",\r\n                4: \"第5级\",\r\n                5: \"第6级\",\r\n              };\r\n              const levelText = depthMap[params.data.depth] || \"未知层级\";\r\n              return `${params.data.name}<br/>类别: ${\r\n                params.data.category || \"未知\"\r\n              }<br/>层级: ${levelText}`;\r\n            }\r\n          },\r\n        },\r\n        series: [\r\n          {\r\n            type: \"sankey\",\r\n            layout: \"none\",\r\n            emphasis: {\r\n              focus: \"adjacency\",\r\n            },\r\n            data: this.sankeyData.nodes,\r\n            links: this.sankeyData.links,\r\n            // orient: 'vertical',\r\n            // nodeAlign: 'justify',\r\n            nodeGap: 10,\r\n            nodeWidth: 40,\r\n            layoutIterations: 0,\r\n            left: \"0\",\r\n            right: \"20%\",\r\n            top: \"0.2%\",\r\n            bottom: \"0.2%\",\r\n            label: {\r\n              show: true,\r\n              position: \"right\",\r\n              color: \"#ffffff\",\r\n              fontSize: 14,\r\n              formatter: function (params) {\r\n                return params.name.length > 12\r\n                  ? params.name.substring(0, 12) + \"...\"\r\n                  : params.name;\r\n              },\r\n            },\r\n            lineStyle: {\r\n              color: \"source\",\r\n              // curveness: 0.2,\r\n              // opacity: 0.7\r\n            },\r\n            // itemStyle: {\r\n            //   borderWidth: 1,\r\n            //   borderColor: '#0ec2f4'\r\n            // }\r\n          },\r\n        ],\r\n      };\r\n    },\r\n\r\n    initChart() {\r\n      if (!this.hasData) {\r\n        return; // 没有数据时不初始化图表\r\n      }\r\n\r\n      // 设置图表容器的尺寸 - 初始化时使用基础高度\r\n      this.$refs.sankeyChart.style.width = \"100%\";\r\n      this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n\r\n      this.chart = echarts.init(this.$refs.sankeyChart);\r\n\r\n      // 初始化时设置空的图表配置\r\n      const option = this.getChartOption();\r\n      this.chart.setOption(option);\r\n\r\n      // 添加节点点击事件\r\n      this.chart.on(\"click\", (params) => {\r\n        if (params.dataType === \"node\" && params.data.category === \"子标签\") {\r\n          this.toggleEnterpriseNodes(params.data.id);\r\n        }\r\n      });\r\n\r\n      // 设置尺寸变化监听\r\n      this.setupResizeListeners();\r\n    },\r\n\r\n    // 切换企业节点的显示/隐藏\r\n    toggleEnterpriseNodes(subLabelId) {\r\n      if (this.expandedSubLabels.has(subLabelId)) {\r\n        // 如果已展开，则收起\r\n        this.expandedSubLabels.delete(subLabelId);\r\n      } else {\r\n        // 如果未展开，则展开\r\n        this.expandedSubLabels.add(subLabelId);\r\n      }\r\n\r\n      // 重新过滤数据\r\n      const newSankeyData = this.filterEnterpriseNodes(this.originalData);\r\n\r\n      // 计算新高度\r\n      const enterpriseCount = newSankeyData.nodes.filter(\r\n        (node) => node.category === \"企业\"\r\n      ).length;\r\n      const newHeight = `${\r\n        this.baseHeight + enterpriseCount * this.heightPerExpansion\r\n      }px`;\r\n\r\n      // 同时更新数据、高度和图表，确保完全同步\r\n      this.sankeyData = newSankeyData;\r\n      if (this.$refs.sankeyChart) {\r\n        this.$refs.sankeyChart.style.height = newHeight;\r\n      }\r\n\r\n      // 如果没有任何展开的子领域（所有企业都收起），回到顶部并恢复初始高度\r\n      if (this.expandedSubLabels.size === 0) {\r\n        // 先强制设置高度为基础高度\r\n        if (this.$refs.sankeyChart) {\r\n          this.$refs.sankeyChart.style.height = `${this.baseHeight}px`;\r\n          // 强制浏览器重新计算布局\r\n          this.$refs.sankeyChart.offsetHeight;\r\n        }\r\n\r\n        // 立即更新图表以确保高度生效\r\n        if (this.chart) {\r\n          this.chart.resize();\r\n        }\r\n\r\n        // 然后滚动到顶部\r\n        const container = this.$refs.sankeyChart.parentElement;\r\n        if (container) {\r\n          // 强制触发重绘\r\n          container.offsetHeight;\r\n          container.scrollTop = 0;\r\n          // 使用 scrollTo 确保滚动生效\r\n          container.scrollTo({ top: 0, behavior: \"instant\" });\r\n        }\r\n      }\r\n\r\n      // 立即更新图表\r\n      this.updateChart();\r\n      if (this.chart) {\r\n        this.chart.resize();\r\n      }\r\n    },\r\n\r\n    // 设置尺寸变化监听\r\n    setupResizeListeners() {\r\n      // 创建 ResizeObserver 监听容器尺寸变化\r\n      if (window.ResizeObserver) {\r\n        this.resizeObserver = new ResizeObserver(() => {\r\n          // 使用防抖处理，避免频繁触发\r\n          clearTimeout(this.resizeTimer);\r\n          this.resizeTimer = setTimeout(() => {\r\n            this.handleResize();\r\n          }, 100);\r\n        });\r\n\r\n        // 监听图表容器的尺寸变化\r\n        this.resizeObserver.observe(this.$refs.sankeyChart);\r\n\r\n        // 也监听父容器的尺寸变化\r\n        const parentContainer = this.$refs.sankeyChart.parentElement;\r\n        if (parentContainer) {\r\n          this.resizeObserver.observe(parentContainer);\r\n        }\r\n      }\r\n\r\n      // 监听窗口大小变化（作为备用方案）\r\n      this.handleResize = this.handleResize.bind(this);\r\n      window.addEventListener(\"resize\", this.handleResize);\r\n    },\r\n\r\n    // 处理尺寸变化\r\n    handleResize() {\r\n      if (this.chart) {\r\n        // 延迟执行 resize，确保 DOM 更新完成\r\n        this.$nextTick(() => {\r\n          this.chart.resize();\r\n        });\r\n      }\r\n    },\r\n\r\n    // 手动触发图表重新调整大小（供父组件调用）\r\n    resizeChart() {\r\n      this.handleResize();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bg-box {\r\n  position: relative;\r\n  background: #1b283b;\r\n  border-radius: 8px;\r\n  padding: 8px 16px 16px;\r\n  margin-bottom: 20px;\r\n\r\n  .bg-box-title {\r\n    font-weight: 800;\r\n    font-size: 18px;\r\n    color: #ffffff;\r\n    height: 30px;\r\n    line-height: 30px;\r\n    margin-bottom: 10px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .bg-box-content {\r\n    font-size: 16px;\r\n    color: #ffffff;\r\n    white-space: pre-wrap;\r\n  }\r\n}\r\n\r\n.date-picker-container {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.title-date-picker {\r\n  width: 240px;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner {\r\n  background: rgba(27, 40, 59, 0.8) !important;\r\n  border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  color: #ffffff !important;\r\n  font-size: 12px !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-input__inner::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-separator {\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input {\r\n  background: transparent !important;\r\n  color: #ffffff !important;\r\n}\r\n\r\n.date-picker-container ::v-deep .el-range-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n}\r\n\r\n// 额外的强制样式覆盖\r\n::v-deep .el-date-editor.el-input {\r\n  .el-input__wrapper {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n  }\r\n\r\n  .el-input__inner {\r\n    background: rgba(27, 40, 59, 0.8) !important;\r\n    border: 1px solid rgba(14, 194, 244, 0.3) !important;\r\n    color: #ffffff !important;\r\n  }\r\n}\r\n\r\n.sankey-container {\r\n  overflow-y: auto; /* 允许垂直滚动 */\r\n  overflow-x: hidden;\r\n  position: relative;\r\n}\r\n\r\n.sankey-chart {\r\n  width: 100%;\r\n  min-height: 600px; /* 设置最小高度，确保图表有足够空间渲染 */\r\n  /* 完全移除过渡动画，实现瞬间变化 */\r\n}\r\n\r\n.no-data-container {\r\n  width: 100%;\r\n  height: 100px; /* 无数据时的较小高度 */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.no-data-text {\r\n  color: #fff;\r\n  font-size: 16px;\r\n  text-align: center;\r\n}\r\n\r\n/* 自定义滚动条样式 */\r\n.sankey-container::-webkit-scrollbar,\r\n.sankey-fullscreen-container::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-track,\r\n.sankey-fullscreen-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb {\r\n  background: rgba(14, 194, 244, 0.6);\r\n  border-radius: 4px;\r\n}\r\n\r\n.sankey-container::-webkit-scrollbar-thumb:hover,\r\n.sankey-fullscreen-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(14, 194, 244, 0.8);\r\n}\r\n</style>\r\n"]}]}