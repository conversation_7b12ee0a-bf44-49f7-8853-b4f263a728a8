<template>
  <div v-if="visible" class="custom-dialog-mask" @click="handleMaskClick">
    <div
      class="custom-dialog"
      :class="{ 'enterprise-info-fullscreen': isFullscreen }"
      :style="isFullscreen ? {} : { width: width + 'px' }"
      @click.stop
    >
      <div class="custom-dialog-header">
        <span>{{ title }}</span>
        <div style="display: flex; align-items: center">
          <div
            @click="handleScreen"
            :title="isFullscreen ? '退出全屏' : '全屏'"
            style="
              margin-right: 20px;
              cursor: pointer;
              color: #ffffff;
              font-size: 20px;
            "
          >
            <i
              :class="isFullscreen ? 'el-icon-rank' : 'el-icon-full-screen'"
              style="width: 20px; height: 20px"
            ></i>
          </div>
          <div class="custom-dialog-close" @click="closeDialog"></div>
        </div>
      </div>
      <div class="custom-dialog-body">
        <div class="bg-box">
          <div class="bg-box-title">一、企业基本信息</div>
          <div class="bg-box-content">
            <div class="bg-box-content-list">
              <span>企业名称：</span>
              {{ content.enterpriseName }}
            </div>
            <div class="bg-box-content-list">
              <span>企业简介：</span>
              <div
                v-if="content.summary"
                v-html="content.summary.replace(/\n/g, '<br/>')"
              ></div>
            </div>
          </div>
        </div>
        <!-- 企业关系桑葚图 -->
        <sankeyChart2
          title="二、企业相关关系"
          param-type="enterpriseName"
          :param-value="content.enterpriseName"
          style="width: 100%"
        />
        <div class="bg-box">
          <div class="bg-box-title">三、企业相关新闻</div>
          <div
            class="bg-box-content"
            v-loading="loading"
            element-loading-background="rgba(27, 40, 59, 0.8)"
            style="min-height: 100px"
          >
            <ul class="article-list" v-if="articles.length > 0">
              <li
                v-for="article in articles"
                :key="article.id"
                class="article-item"
              >
                <div class="article-title" @click="openDetail(article)">
                  {{ article.cnTitle }}
                </div>
                <div class="article-publishTime">
                  {{ formatPublishTime(article.publishTime) }}
                </div>
              </li>
            </ul>
            <div v-else-if="!loading">
              <div class="no-data">暂无数据</div>
            </div>
            <pagination
              v-show="total3 > 0"
              :total="total3"
              :page.sync="queryParams3.pageNum"
              :limit.sync="queryParams3.pageSize"
              @pagination="getList3"
            />
          </div>
        </div>
        <div class="bg-box" v-if="total1 > 0">
          <div class="bg-box-title">四、企业相关知识产权</div>
          <div class="bg-box-content">
            <el-table :data="patentList" border style="width: 100%">
              <el-table-column
                prop="patentName"
                align="center"
                label="专利名称"
                width="300"
              >
                <template slot-scope="scope">
                  <el-tooltip
                    effect="light"
                    :content="scope.row.patentName"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="ellipsis-text">{{ scope.row.patentName }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="patentNumber"
                align="center"
                label="专利号"
              />
              <el-table-column
                prop="publishTime"
                align="center"
                label="申请日期"
              />
              <el-table-column
                prop="filingTime"
                align="center"
                label="授权日期"
              />
            </el-table>
            <pagination
              v-show="total1 > 0"
              :total="total1"
              :page.sync="queryParams1.pageNum"
              :limit.sync="queryParams1.pageSize"
              @pagination="getList1"
            />
          </div>
        </div>
        <div class="bg-box" style="padding-top: 16px" v-if="total2 > 0">
          <div class="bg-box-title" v-if="total1 == 0">
            四、企业相关知识产权
          </div>
          <div class="bg-box-content">
            <el-table :data="softwareList" border style="width: 100%">
              <el-table-column
                prop="softwareName"
                align="center"
                label="软件全称"
                width="300"
              >
                <template slot-scope="scope">
                  <el-tooltip
                    effect="light"
                    :content="scope.row.softwareName"
                    placement="top"
                    :hide-after="0"
                  >
                    <div class="ellipsis-text">
                      {{ scope.row.softwareName }}
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column
                prop="softwareAbbreviation"
                align="center"
                label="软件简称"
              />
              <el-table-column
                prop="registrationNo"
                align="center"
                label="登记号"
              />
              <el-table-column
                prop="completionDate"
                align="center"
                label="开发完成日期"
              />
              <el-table-column
                prop="firstReleaseDate"
                align="center"
                label="首次发布日期"
              />
              <el-table-column
                prop="registrationDate"
                align="center"
                label="登记日期"
              />
            </el-table>
            <pagination
              v-show="total2 > 0"
              :total="total2"
              :page.sync="queryParams2.pageNum"
              :limit.sync="queryParams2.pageSize"
              @pagination="getList2"
            />
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">
            {{ total1 > 0 && total2 > 0 ? "五" : "四" }}、针对上述内容的观点
          </div>
          <div class="bg-box-content">
            <div
              v-if="content.viewpoint"
              v-html="content.viewpoint.replace(/\n/g, '<br/>')"
            ></div>
          </div>
        </div>
        <div class="bg-box">
          <div class="bg-box-title">
            {{ total1 > 0 && total2 > 0 ? "六" : "五" }}、被打压后对企业的影响
          </div>
          <div class="bg-box-content">
            <div
              v-if="content.impact"
              v-html="content.impact.replace(/\n/g, '<br/>')"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getXyArticleListByPage } from "@/api/bigScreen/sanhao.js";
import sankeyChart2 from "../components/sankeyChart2.vue";

export default {
  name: "EnterpriseInformation",
  components: {
    sankeyChart2,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "自定义弹窗",
    },
    closeOnClickMask: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 1000,
    },
    content: {
      type: Object,
      default: () => ({}),
    },
    total1: {
      type: Number,
      default: 0,
    },
    total2: {
      type: Number,
      default: 0,
    },
    patentList: {
      type: Array,
      default: () => [],
    },
    softwareList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      queryParams1: {
        pageNum: 1,
        pageSize: 10,
      },
      queryParams2: {
        pageNum: 1,
        pageSize: 10,
      },
      articles: [],
      queryParams3: {
        pageNum: 1,
        pageSize: 10,
      },
      total3: 0,
      loading: false,
      // 全屏状态
      isFullscreen: false,
    };
  },
  mounted() {
    // 添加ESC键监听
    document.addEventListener("keydown", this.handleKeydown);
    // 添加窗口大小变化监听
    window.addEventListener("resize", this.handleWindowResize);
  },

  beforeDestroy() {
    // 移除ESC键监听
    document.removeEventListener("keydown", this.handleKeydown);
    // 移除窗口大小变化监听
    window.removeEventListener("resize", this.handleWindowResize);
  },

  watch: {
    visible: {
      handler(newVisible) {
        if (newVisible) {
          // 重置全屏状态
          this.isFullscreen = false;
          this.$nextTick(() => {
            this.queryParams3 = {
              pageNum: 1,
              pageSize: 10,
            };
            this.articles = [];
            this.total3 = 0;
            this.getList3();
          });
        }
      },
    },
  },
  methods: {
    // 关闭弹窗的方法
    closeDialog() {
      this.$emit("update:visible", false);
    },

    getList1() {
      this.$emit("pagination1", this.content.suppressSn, this.queryParams1);
    },

    getList2() {
      this.$emit("pagination2", this.content.suppressSn, this.queryParams2);
    },

    // 处理遮罩层点击事件
    handleMaskClick() {
      if (this.closeOnClickMask) {
        this.closeDialog();
      }
    },
    getList3() {
      this.loading = true;
      getXyArticleListByPage({
        type: "0",
        current: this.queryParams3.pageNum,
        size: this.queryParams3.pageSize,
        keywords: this.content.enterpriseName,
      })
        .then((res) => {
          const originalArticles = res.data?.records || [];
          this.articles = this.deduplicateArticles(originalArticles);
          this.total3 = res.data?.total || 0;
          this.loading = false;
        })
        .catch((error) => {
          console.error("获取企业相关新闻失败:", error);
          this.articles = [];
          this.total3 = 0;
          this.loading = false;
        });
    },
    openDetail(item) {
      this.$emit("openArticleDetail", item);
    },

    // 格式化发布时间，去掉时分秒
    formatPublishTime(publishTime) {
      if (!publishTime) return "";
      // 如果是日期字符串，提取年月日部分
      if (typeof publishTime === "string") {
        return publishTime.split(" ")[0];
      }
      // 如果是Date对象，格式化为YYYY-MM-DD
      if (publishTime instanceof Date) {
        return publishTime.toISOString().split("T")[0];
      }
      return publishTime;
    },

    // 去重文章，相同标题只保留一条并标记数量
    deduplicateArticles(articles) {
      if (!articles || articles.length === 0) return [];

      const titleMap = new Map();

      // 统计相同标题的文章
      articles.forEach((article) => {
        if (!article.cnTitle) return;

        // 去掉标题中的所有空格进行比较
        const normalizedTitle = article.cnTitle.replace(/\s+/g, "");

        if (titleMap.has(normalizedTitle)) {
          titleMap.get(normalizedTitle).count++;
        } else {
          titleMap.set(normalizedTitle, {
            article: { ...article },
            count: 1,
          });
        }
      });

      // 生成去重后的文章列表
      const deduplicatedArticles = [];
      titleMap.forEach(({ article, count }) => {
        if (count > 1) {
          // 如果有重复，在标题后添加数量标识
          article.cnTitle = `${article.cnTitle}(${count})`;
        }
        deduplicatedArticles.push(article);
      });

      return deduplicatedArticles;
    },

    // 全屏切换
    handleScreen() {
      this.isFullscreen = !this.isFullscreen;

      // 延迟调整图表大小，确保DOM更新完成
      this.$nextTick(() => {
        setTimeout(() => {
          // 如果有图表组件，调整其大小
          // 这里可以根据实际的图表组件引用进行调整
        }, 300); // 等待CSS动画完成
      });
    },

    // 处理键盘事件
    handleKeydown(event) {
      // 按ESC键退出全屏
      if (event.key === "Escape" && this.isFullscreen && this.visible) {
        this.isFullscreen = false;
      }
    },

    // 处理窗口大小变化
    handleWindowResize() {
      if (this.isFullscreen) {
        // 重新调整图表大小
        // 这里可以根据实际的图表组件引用进行调整
      }
    },
  },
};
</script>

<style scoped lang="scss">
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  // 确保在所有分辨率下都能正确覆盖
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;

  .custom-dialog {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    width: 500px;
    border: 10px solid;
    border-right-width: 5px;
    border-left-width: 5px;
    border-image: url("../../../assets/bigScreenSanhao/dialogBg.png") 27 round;
    background-color: #000000d0;
    padding-bottom: 20px;
    transition: all 0.3s ease;

    &.enterprise-info-fullscreen {
      width: calc(100vw - 40px) !important;
      height: calc(100vh - 40px) !important;
      max-width: none !important;
      max-height: none !important;
      margin: 0 !important;
      // 确保在所有分辨率下都能正确显示
      min-width: calc(100% - 40px) !important;
      min-height: calc(100% - 40px) !important;

      .custom-dialog-body {
        height: calc(100% - 80px); // 减去header高度和padding
        max-height: calc(100% - 80px);
        overflow-y: auto;
        overflow-x: hidden;
      }
    }

    .custom-dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px 0 5%;
      margin: 10px -3px 20px;
      background-image: url("../../../assets/bigScreenSanhao/dialogTitle.png");
      background-size: 100% 100%;
      height: 50px;
      font-weight: 600;
      font-size: 22px;
      color: #ffffff;
      line-height: 50px;

      span {
        padding-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .custom-dialog-close {
        width: 20px;
        height: 20px;
        background-image: url("../../../assets/bigScreenSanhao/dialogClose.png");
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

    .custom-dialog-body {
      height: 800px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0px 20px 0px;

      .bg-box {
        background: #1b283b;
        border-radius: 8px 8px 8px 8px;
        padding: 8px 16px 16px;
        margin-bottom: 20px;

        .bg-box-title {
          font-weight: 800;
          font-size: 18px;
          color: #ffffff;
          height: 30px;
          line-height: 30px;
          margin-bottom: 10px;
        }

        .bg-box-content {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.9);

          ::v-deep .el-table__header th {
            background-color: #1f3850 !important;
            color: rgba(255, 255, 255);
            font-size: 16px;
          }

          ::v-deep .el-table__body td {
            background-color: #1d3046;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
          }

          .bg-box-content-list {
            // padding: 0 0 10px 15px;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);

            span {
              font-weight: 600;
              color: rgba(255, 255, 255, 0.9);
              margin-right: 10px;
            }
          }
        }
      }

      .article-list {
        list-style: none;
        padding: 0;
        margin: 0;

        .article-item {
          display: flex;
          height: 40px;
          line-height: 40px;
          margin-bottom: 10px;
          font-size: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .article-title {
            cursor: pointer;
            width: calc(100% - 100px);
            font-size: 18px;
            // font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .article-publishTime {
            width: 100px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  color: #fff;
  font-size: 16px;
  height: 100px;
  line-height: 80px;
}

::v-deep .pagination-container {
  background-color: #2a304000;
  color: #f2f2f2;
  height: 55px;
  margin: 20px 0 0;
  padding-bottom: 0px !important;

  .el-select__wrapper,
  .el-input__wrapper {
    .el-select__placeholder {
      color: #fff;
    }

    background: #2a304000;
    border-color: #ffffff;
  }

  .el-input__inner {
    color: #fff;
  }
}

::v-deep .el-pagination__total,
::v-deep .el-pagination__jump {
  color: #f2f2f2;
}

::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next,
::v-deep .el-pagination button:disabled {
  background-color: #ffffff00 !important;
  color: #fff !important;
}

::v-deep .el-pager li {
  background: #ffffff00 !important;
  color: #fff !important;

  &.active {
    color: #1890ff !important;
  }
}

// 添加多行文本省略样式
.ellipsis-text {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.5;
}
</style>
