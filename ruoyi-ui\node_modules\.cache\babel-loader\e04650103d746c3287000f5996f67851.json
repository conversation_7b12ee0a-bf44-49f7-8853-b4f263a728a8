{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\views\\components\\briefingPreview.vue", "mtime": 1753690547255}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_renderLine", "require", "_utils", "_index", "echarts", "_interopRequireWildcard", "_briefing", "_interopRequireDefault", "docx", "_default", "exports", "default", "props", "preViewData", "required", "type", "Object", "data", "tableData", "downloading", "myChart", "infoData", "content", "detectionTime", "xAxis", "timer", "wechatList", "mounted", "formatDate", "created", "flag", "downloadBrieFing", "isWechat", "StatisticalList", "getWorld", "getInfoData", "methods", "renderLineCharts", "chrtsDataHandle", "dom", "titleShow", "render", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "API", "exportWorld", "reportId", "briefingId", "then", "res", "renderAsync", "$refs", "file", "$store", "commit", "stop", "renderBar", "chartDom", "document", "getElementById", "init", "option", "max", "tooltip", "trigger", "axisPointer", "yAxis", "boundaryGap", "grid", "left", "right", "bottom", "containLabel", "series", "setOption", "_this2", "_callee2", "_callee2$", "_context2", "downLoadXls", "setTimeout", "$message", "message", "switchPreview", "a", "createElement", "href", "window", "URL", "createObjectURL", "download", "click", "querySelector", "getPdf", "setInterval", "ReportStatisics", "$emit", "_this3", "_callee3", "_callee3$", "_context3", "previewData", "JSON", "parse", "title", "hotList", "description", "error", "abrupt", "briefingInfo", "sent", "code", "openNewViewWechat", "item", "shortUrl", "sourceType", "open", "originalUrl", "_this4", "WeArr", "<PERSON><PERSON><PERSON><PERSON>", "Xdata", "end_Time", "start_Time", "Year", "endYear", "dateType", "length", "fill", "i", "push", "concat", "toString", "keys", "wyTrendCount", "for<PERSON>ach", "key", "slice", "lastIndexOf", "wxTrendCount", "Number", "endTime", "startTime", "end", "start", "num", "index", "Wx", "WEB", "name", "fileName", "reportName", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "_this5", "_callee4", "_callee4$", "_context4", "dialogTableVisible", "statistics", "pageSize", "pageNum", "pageNum1", "rows", "openNewView", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval"], "sources": ["src/views/components/briefingPreview.vue"], "sourcesContent": ["<!-- 简报预览 -->\r\n<template>\r\n  <div>\r\n    <div class=\"PreviewMain\" id=\"brieFing\">\r\n      <div class=\"topTool\">\r\n        <div class=\"callback\" @click=\"switchPreview\">\r\n          <i class=\"el-icon-arrow-left iconCallback\"></i>返回\r\n        </div>\r\n        <el-button\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          @click=\"ReportStatisics\"\r\n          ref=\"down\"\r\n          v-if=\"preViewData.reportStatus == 1\"\r\n          >生成报告</el-button\r\n        >\r\n        <el-button\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          class=\"download\"\r\n          @click=\"downloadBrieFing('download')\"\r\n          ref=\"down\"\r\n          v-if=\"!preViewData.preview\"\r\n          >下载报告</el-button\r\n        >\r\n        {{ preViewData.preView }}\r\n      </div>\r\n      <div class=\"title\">\r\n        <h1>{{ infoData.title }}</h1>\r\n        <h5>{{ content ? content.detectionTime : null }}</h5>\r\n      </div>\r\n      <!-- <div class=\"describe\">\r\n        <div class=\"BoxHeader\">\r\n          01 报告描述\r\n        </div>\r\n        <div class=\"cellStyle\">\r\n          <ul>\r\n            <li v-for=\"(item, key) in content.description\" :key=\"key\">\r\n              {{ item }}\r\n            </li>\r\n          </ul>\r\n        </div>\r\n                </div>  2023 9-4   沈老师  暂时注释-->\r\n      <div class=\"detail-container\">\r\n        <template v-if=\"!preViewData.isWechat\">\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">01 事件走势</div>\r\n            <span\r\n              style=\"position: relative; top: 20px; left: 55px; font-size: 14px\"\r\n              v-if=\"content\"\r\n              >{{ content ? content.eventDesc : null }}</span\r\n            >\r\n            <div class=\"charts\" id=\"line\"></div>\r\n          </div>\r\n          <div class=\"describe\">\r\n            <div class=\"BoxHeader\">02 热门文章</div>\r\n            <div class=\"tableStyle\">\r\n              <div>\r\n                <el-table\r\n                  size=\"mini\"\r\n                  :data=\"tableData\"\r\n                  border\r\n                  style=\"width: 92.5%\"\r\n                  :header-cell-style=\"{\r\n                    textAlign: 'center',\r\n                    backgroundColor: 'rgb(64, 158, 255)',\r\n                    color: '#ffff',\r\n                  }\"\r\n                  :cell-style=\"{}\"\r\n                >\r\n                  <el-table-column\r\n                    type=\"index\"\r\n                    label=\"序号\"\r\n                    width=\"60\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"cnTitle\"\r\n                    label=\"标题\"\r\n                    :show-overflow-tooltip=\"true\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      <span @click=\"openNewView(scope.row)\" class=\"tableTitle\">\r\n                        {{ scope.row.cnTitle }}\r\n                      </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishType\"\r\n                    label=\"平台类型\"\r\n                    width=\"130\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{\r\n                        scope.row.sourceType == 1\r\n                          ? \"微信公众号\"\r\n                          : null || scope.row.sourceType == 2\r\n                          ? \"网站\"\r\n                          : null || scope.row.sourceType == 3\r\n                          ? \"手动录入\"\r\n                          : null\r\n                      }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column\r\n                    prop=\"sourceName\"\r\n                    label=\"媒体来源\"\r\n                    width=\"300\"\r\n                    align=\"center\"\r\n                  ></el-table-column>\r\n                  <el-table-column\r\n                    prop=\"publishTime\"\r\n                    label=\"发布时间\"\r\n                    width=\"180\"\r\n                    align=\"center\"\r\n                  >\r\n                    <template slot-scope=\"scope\">\r\n                      {{ formatDate(scope.row.publishTime) }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"preViewData.isWechat == 1\">\r\n          <div class=\"title\" style=\"font-weight: 600; font-size: 20px\">\r\n            {{ preViewData.reportName }}报送条目\r\n          </div>\r\n          <!-- <div class=\"title\" style=\"font-weight: 100;font-size: 32px;margin: 20px auto;width: 50%;font-family: cursive;\">\r\n          （★条目为“境外媒体关于我国的文章”，已由国内翻译、转载，并审核溯源可靠、内容准确。）</div> -->\r\n          <div\r\n            v-for=\"(item, index) in wechatList\"\r\n            :key=\"index\"\r\n            class=\"worldStyle\"\r\n          >\r\n            <p class=\"cnSummary\">\r\n              <!-- {{ index + 1 + \".微信公众号\" + item.sourceName }} -->\r\n              {{ index + 1 + \".\" + item.sourceName }}\r\n              {{\r\n                item.publishTime\r\n                  ? new Date(item.publishTime).getMonth() +\r\n                    1 +\r\n                    \"月\" +\r\n                    new Date(item.publishTime).getDate() +\r\n                    \"日\"\r\n                  : \"\"\r\n              }}\r\n              {{ \"报道:\" + (item.cnSummary ? item.cnSummary : \"暂无摘要\") }}\r\n            </p>\r\n            <p class=\"link\" @click=\"openNewViewWechat(item)\">\r\n              {{ \"(\" + (item.shortUrl ? item.shortUrl : \"暂无链接\") + \")\" }}\r\n            </p>\r\n          </div>\r\n        </template>\r\n        <div class=\"docx-container\">\r\n          <div ref=\"file\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { renderLineCharts, renderAnnular } from \"@/utils/renderLine.js\";\r\nimport { antiShake } from \"@/utils/utils.js\";\r\nimport { formatDate } from \"@/utils/index.js\";\r\nimport * as echarts from \"echarts\";\r\nimport API from \"@/api/ScienceApi/briefing.js\";\r\n// 引入docx-preview插件\r\nlet docx = require(\"docx-preview\");\r\nexport default {\r\n  props: {\r\n    preViewData: {\r\n      required: true,\r\n      type: Object,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      tableData: [],\r\n      downloading: false,\r\n      myChart: null,\r\n      infoData: {},\r\n      content: {\r\n        detectionTime: \"\",\r\n      },\r\n      xAxis: {},\r\n      timer: null,\r\n      wechatList: [],\r\n    };\r\n  },\r\n  mounted() {\r\n    this.formatDate = formatDate;\r\n  },\r\n  created() {\r\n    if (this.preViewData.flag) {\r\n      this.downloadBrieFing();\r\n    }\r\n\r\n    /* 微信公众号模板 */\r\n    if (this.preViewData.isWechat == 1) {\r\n      this.StatisticalList();\r\n    } else if (this.preViewData.isWechat == 2) {\r\n      this.getWorld();\r\n    } else {\r\n      this.getInfoData();\r\n    }\r\n  },\r\n  methods: {\r\n    renderLineCharts() {\r\n      let data = this.chrtsDataHandle();\r\n      new renderLineCharts({\r\n        dom: \"line\",\r\n        data: data,\r\n        titleShow: false,\r\n        xAxis: this.xAxis,\r\n      }).render();\r\n      // this.renderAnnular()\r\n    },\r\n    async getWorld() {\r\n      await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n        (res) => {\r\n          docx.renderAsync(res, this.$refs.file); // 渲染到页面\r\n          this.$store.commit(\"app/set_Loding\", false);\r\n        }\r\n      );\r\n    },\r\n    renderBar() {\r\n      var chartDom = document.getElementById(\"bar\");\r\n      this.myChart = echarts.init(chartDom);\r\n      var option;\r\n      option = {\r\n        xAxis: {\r\n          type: \"value\",\r\n          max: 250,\r\n        },\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"shadow\",\r\n          },\r\n        },\r\n        yAxis: {\r\n          type: \"category\",\r\n          boundaryGap: [0, 0.01],\r\n          data: [\"微博\", \"微信\", \"今日头条\", \"网易新闻\", \"腾讯新闻\"],\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true,\r\n        },\r\n        series: [\r\n          {\r\n            data: [120, 200, 150, 80, 70, 110, 130],\r\n            type: \"bar\",\r\n          },\r\n        ],\r\n      };\r\n      option && this.myChart.setOption(option);\r\n    },\r\n    async downloadBrieFing(flag) {\r\n      this.$store.commit(\"app/set_Loding\", true);\r\n      /* 使用微信公众号模板 */\r\n      if (this.preViewData.isWechat) {\r\n        await API.exportWorld({ reportId: this.preViewData.briefingId }).then(\r\n          (res) => {\r\n            if (this.preViewData.isWechat == 1) {\r\n              this.downLoadXls(res);\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            } else if (this.preViewData.isWechat == 2) {\r\n              let a = document.createElement(\"a\");\r\n              a.href = window.URL.createObjectURL(res);\r\n              a.download = \"briefing.docx\";\r\n              a.click();\r\n              this.timer = setTimeout(() => {\r\n                this.$message({ message: \"下载完成\", type: \"success\" });\r\n                this.switchPreview();\r\n              }, 1000);\r\n            }\r\n          }\r\n        );\r\n      } else {\r\n        /* 使用普通模板 */\r\n        let dom = document.querySelector(\"#brieFing\");\r\n\r\n        if (dom) {\r\n          this.$store.commit(\"app/set_Loding\", true);\r\n          this.getPdf(dom);\r\n          setTimeout(() => {\r\n            this.$store.commit(\"app/set_Loding\", false);\r\n          }, 2000);\r\n        } else {\r\n          this.timer = setInterval(() => {\r\n            dom = document.querySelector(\"#brieFing\");\r\n            if (dom) {\r\n              this.getPdf(dom);\r\n              this.switchPreview();\r\n              this.$message({ message: \"下载成功\", type: \"success\" });\r\n            }\r\n          }, 2000);\r\n        }\r\n      }\r\n    },\r\n    ReportStatisics() {\r\n      this.$emit(\"ReportStatisics\");\r\n    },\r\n    /* 获取详情数据 */\r\n    async getInfoData() {\r\n      let res;\r\n      /* 当前为生成前预览 */\r\n      if (this.preViewData.previewData) {\r\n        this.content = JSON.parse(this.preViewData.previewData);\r\n        this.infoData.title = this.content.title;\r\n        this.tableData = this.content.hotList;\r\n        this.description = this.content.description;\r\n        setTimeout(() => {\r\n          try {\r\n            this.renderLineCharts();\r\n          } catch (error) {}\r\n        }, 200);\r\n        return;\r\n      }\r\n      res = await API.briefingInfo(this.preViewData.briefingId);\r\n      if (res.code == 200) {\r\n        this.infoData = res.data;\r\n        this.content = JSON.parse(res.data.content);\r\n        if (this.content) {\r\n          this.tableData = this.content.hotList;\r\n          this.description = this.content.description;\r\n        }\r\n      } else {\r\n        this.$message({ message: \"数据获取失败\", type: \"error\" });\r\n      }\r\n      try {\r\n        this.renderLineCharts();\r\n      } catch (error) {}\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewViewWechat(item) {\r\n      if (!item.shortUrl) return;\r\n      if (item.sourceType == 1) {\r\n        window.open(item.shortUrl);\r\n      } else if (item.sourceType == 2) {\r\n        window.open(item.originalUrl);\r\n      }\r\n    },\r\n    /* 图表数据处理 */\r\n    chrtsDataHandle() {\r\n      let WeArr = [],\r\n        WyArr = [],\r\n        Xdata = [],\r\n        end_Time,\r\n        start_Time,\r\n        Year,\r\n        endYear;\r\n      /* 当前是报告详情 */\r\n      if (!this.preViewData.previewData && this.content.dateType) {\r\n        switch (this.content.dateType) {\r\n          case \"hour\" /* 按小时计算 */:\r\n            WeArr.length = 24;\r\n            WyArr.length = 24;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 处理x轴数据 */\r\n            for (let i = 0; i < 24; i++) {\r\n              Xdata.push(`${i.toString().length == 1 ? \"0\" + i : i}:00`);\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WyArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(11, 13) + \":00\";\r\n              WeArr[Xdata.lastIndexOf(key)] = this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"day\":\r\n            (end_Time = Number(this.content.endTime.slice(8, 10))),\r\n              (start_Time = Number(this.content.startTime.slice(8, 10))),\r\n              (Year = this.content.startTime.slice(0, 7));\r\n            endYear = this.content.endTime.slice(0, 7);\r\n            /* 跨越两个月的情况 */\r\n            let end = Number(this.content.endTime.slice(5, 7)),\r\n              start = Number(this.content.startTime.slice(5, 7)),\r\n              num = 30 - Number(start_Time);\r\n            if (end > start) {\r\n              end_Time = end_Time + num;\r\n            }\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            /* 数据填充 */\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            /* 循环数据 */\r\n            let a,\r\n              i = 1;\r\n            while (Xdata.length < end_Time) {\r\n              a = i;\r\n              let item;\r\n              if (start_Time <= 30) {\r\n                i = start_Time;\r\n              } else if (start_Time <= 31) {\r\n                i = 1;\r\n                a = 1;\r\n              }\r\n              if (start_Time > 30) {\r\n                item = endYear + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              } else {\r\n                item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              }\r\n              Xdata.push(item);\r\n              i = a;\r\n              ++start_Time;\r\n              ++i;\r\n            }\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(8, 11);\r\n              WeArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n            break;\r\n          case \"month\":\r\n            end_Time = Number(this.content.endTime.slice(5, 7));\r\n            start_Time = Number(this.content.startTime.slice(5, 7));\r\n            Year = this.content.startTime.slice(0, 4);\r\n            WeArr.length = end_Time;\r\n            WyArr.length = end_Time;\r\n            WeArr.fill(0);\r\n            WyArr.fill(0);\r\n            for (let i = start_Time; i <= end_Time; i++) {\r\n              let item = Year + \"-\" + (i.toString().length == 1 ? \"0\" + i : i);\r\n              Xdata.push(item);\r\n            }\r\n\r\n            /* 处理y轴数据 */\r\n            Object.keys(this.content.wyTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              WyArr[Xdata.lastIndexOf(Year + \"-\" + key)] =\r\n                this.content.wyTrendCount[item];\r\n            });\r\n\r\n            Object.keys(this.content.wxTrendCount).forEach((item) => {\r\n              let key = item.slice(5, 7);\r\n              let index = Xdata.lastIndexOf(Year + \"-\" + key);\r\n              WeArr[index] = this.content.wxTrendCount[item];\r\n            });\r\n            break;\r\n          default:\r\n            break;\r\n        }\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      } else {\r\n        /* 当前是草稿详情 */\r\n\r\n        let Wx = this.content.wxTrendCount,\r\n          WEB = this.content.wyTrendCount;\r\n\r\n        Object.keys(Wx).forEach((item) => {\r\n          WeArr.push(Wx[item]);\r\n          Xdata.push(item);\r\n        });\r\n        Object.keys(WEB).forEach((item) => {\r\n          WyArr.push(WEB[item]);\r\n        });\r\n        this.xAxis = {\r\n          type: \"category\",\r\n          data: Xdata,\r\n        };\r\n      }\r\n      return [\r\n        {\r\n          name: \"微信\",\r\n          data: WeArr,\r\n          type: \"line\",\r\n        },\r\n        {\r\n          name: \"网站\",\r\n          data: WyArr,\r\n          type: \"line\",\r\n        },\r\n      ];\r\n    },\r\n    /* 文件流解码 */\r\n    downLoadXls(res) {\r\n      let fileName = this.preViewData.reportName;\r\n      if (\"download\" in document.createElement(\"a\")) {\r\n        const a = document.createElement(\"a\"); //创建一个a标签\r\n        a.download = fileName + \".docx\"; //指定文件名称\r\n        a.style.display = \"none\"; //页面隐藏\r\n        a.href = URL.createObjectURL(res); // href用于下载地址\r\n        document.body.appendChild(a); //插到页面上\r\n        a.click(); //通过点击触发\r\n        URL.revokeObjectURL(a.href); //释放URL 对象\r\n        document.body.removeChild(a); //删掉a标签\r\n      } else {\r\n        //IE10 + 下载\r\n        navigator.msSaveBlob(res, fileName);\r\n      }\r\n    },\r\n    switchPreview() {\r\n      this.$emit(\"switchPreview\");\r\n    },\r\n    /* 文章统计列表 */\r\n    async StatisticalList() {\r\n      this.dialogTableVisible = true;\r\n      let res = await API.statistics({\r\n        pageSize: 99,\r\n        pageNum: this.pageNum1,\r\n        reportId: this.preViewData.briefingId,\r\n      });\r\n      if (res.code == 200) {\r\n        this.wechatList = res.rows;\r\n      }\r\n    },\r\n    /* 打开外网链接 */\r\n    openNewView(item) {\r\n      if (item.sourceType == 1) {\r\n        if (item.shortUrl) {\r\n          window.open(item.shortUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      } else if (item.sourceType == 2) {\r\n        if (item.originalUrl) {\r\n          window.open(item.originalUrl);\r\n          return;\r\n        }\r\n        this.$message({ message: \"该文章没有原文链接\" });\r\n      }\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    clearInterval(this.timer);\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tableTitle:hover {\r\n  color: #228fd3;\r\n  border-bottom: solid 1px #228fd3;\r\n}\r\n\r\n.PreviewMain {\r\n  .topTool {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 20px;\r\n    margin-bottom: 10px;\r\n\r\n    .callback {\r\n      font-size: 16px;\r\n      color: rgb(8, 166, 240);\r\n      cursor: pointer;\r\n    }\r\n\r\n    .iconCallback {\r\n      font-size: 18px;\r\n      color: rgb(8, 166, 240);\r\n    }\r\n  }\r\n\r\n  width: 90%;\r\n  overflow: hidden;\r\n  height: calc(100vh - 100px);\r\n  margin: 0 auto;\r\n  padding-top: 20px;\r\n  border: solid 1px #efefef;\r\n  box-shadow: 0px 2px 11px 9px #efefef;\r\n  margin-top: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n\r\n  .title {\r\n    text-align: center;\r\n    font-weight: 600;\r\n    font-size: 14px;\r\n\r\n    h1 {\r\n      font-size: 30px;\r\n    }\r\n  }\r\n\r\n  .describe {\r\n    width: 98%;\r\n    min-height: 200px;\r\n    margin: 15px auto;\r\n    box-shadow: 4px 6px 4px 2px #efefef;\r\n\r\n    .BoxHeader {\r\n      height: 40px;\r\n      line-height: 40px;\r\n      padding-left: 15px;\r\n      width: 100%;\r\n      border-top: solid 1px #e0dfdf;\r\n      border-left: solid 1px #e0dfdf;\r\n      border-right: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .cellStyle {\r\n      width: 100%;\r\n      height: 160px;\r\n      border: solid 1px #e0dfdf;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      p {\r\n        width: 90%;\r\n        font-size: 14px;\r\n        margin: 0 auto;\r\n      }\r\n\r\n      ul {\r\n        font-size: 14px;\r\n        line-height: 25px;\r\n\r\n        li {\r\n          list-style: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .info {\r\n      font-size: 14px;\r\n      margin-left: 15px;\r\n    }\r\n\r\n    .charts {\r\n      width: 100%;\r\n      height: 300px;\r\n      border: solid 1px #e0dfdf;\r\n    }\r\n\r\n    .tableStyle {\r\n      width: 100%;\r\n      min-height: 400px;\r\n      border: solid 1px #e0dfdf;\r\n\r\n      div {\r\n        margin: 20px auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.world {\r\n  width: 80%;\r\n  margin: 0 auto;\r\n  min-height: 1200px;\r\n  background-color: #228fd3;\r\n}\r\n\r\n.worldStyle {\r\n  width: 50%;\r\n  margin: 0 auto;\r\n  text-overflow: clip;\r\n\r\n  .cnSummary {\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n    text-indent: 2em;\r\n  }\r\n\r\n  .link {\r\n    margin-top: -20px;\r\n    font-weight: 400;\r\n    font-size: 16px;\r\n    line-height: 1.8em;\r\n    font-family: PingFang SC, system-ui, -apple-system, BlinkMacSystemFont,\r\n      Helvetica Neue, Hiragino Sans GB, Microsoft YaHei UI, Microsoft YaHei,\r\n      Arial, sans-serif;\r\n  }\r\n\r\n  .link:hover {\r\n    color: #228fd3;\r\n    border-bottom: solid #228fd3 1px;\r\n  }\r\n}\r\n\r\n.detail-container {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n<style scoped>\r\n.docx-container ::v-deep .docx-wrapper {\r\n  background-color: #fff;\r\n  padding: 20px 20px;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx {\r\n  width: 55vw !important;\r\n  padding: 0rem !important;\r\n  min-height: auto !important;\r\n  box-shadow: none;\r\n  margin-bottom: 0;\r\n  line-height: 50px;\r\n  overflow-y: scroll;\r\n  height: 100vh;\r\n}\r\n\r\n.docx-container ::v-deep .docx-wrapper > section.docx::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAC,uBAAA,CAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAC,sBAAA,CAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA,IAAAO,IAAA,GAAAP,OAAA;AAAA,IAAAQ,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACA;EACAC,KAAA;IACAC,WAAA;MACAC,QAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;QACAC,aAAA;MACA;MACAC,KAAA;MACAC,KAAA;MACAC,UAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA,GAAAA,iBAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAhB,WAAA,CAAAiB,IAAA;MACA,KAAAC,gBAAA;IACA;;IAEA;IACA,SAAAlB,WAAA,CAAAmB,QAAA;MACA,KAAAC,eAAA;IACA,gBAAApB,WAAA,CAAAmB,QAAA;MACA,KAAAE,QAAA;IACA;MACA,KAAAC,WAAA;IACA;EACA;EACAC,OAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,IAAApB,IAAA,QAAAqB,eAAA;MACA,IAAAD,4BAAA;QACAE,GAAA;QACAtB,IAAA,EAAAA,IAAA;QACAuB,SAAA;QACAhB,KAAA,OAAAA;MACA,GAAAiB,MAAA;MACA;IACA;IACAP,QAAA,WAAAA,SAAA;MAAA,IAAAQ,KAAA;MAAA,WAAAC,kBAAA,CAAAhC,OAAA,mBAAAiC,oBAAA,CAAAjC,OAAA,IAAAkC,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAjC,OAAA,IAAAoC,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAC,iBAAA,CAAAC,WAAA;gBAAAC,QAAA,EAAAZ,KAAA,CAAA7B,WAAA,CAAA0C;cAAA,GAAAC,IAAA,CACA,UAAAC,GAAA;gBACAjD,IAAA,CAAAkD,WAAA,CAAAD,GAAA,EAAAf,KAAA,CAAAiB,KAAA,CAAAC,IAAA;gBACAlB,KAAA,CAAAmB,MAAA,CAAAC,MAAA;cACA,CACA;YAAA;YAAA;cAAA,OAAAb,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IACAkB,SAAA,WAAAA,UAAA;MACA,IAAAC,QAAA,GAAAC,QAAA,CAAAC,cAAA;MACA,KAAA/C,OAAA,GAAAhB,OAAA,CAAAgE,IAAA,CAAAH,QAAA;MACA,IAAAI,MAAA;MACAA,MAAA;QACA7C,KAAA;UACAT,IAAA;UACAuD,GAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1D,IAAA;UACA;QACA;QACA2D,KAAA;UACA3D,IAAA;UACA4D,WAAA;UACA1D,IAAA;QACA;QACA2D,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,MAAA,GACA;UACAhE,IAAA;UACAF,IAAA;QACA;MAEA;MACAsD,MAAA,SAAAjD,OAAA,CAAA8D,SAAA,CAAAb,MAAA;IACA;IACAtC,gBAAA,WAAAA,iBAAAD,IAAA;MAAA,IAAAqD,MAAA;MAAA,WAAAxC,kBAAA,CAAAhC,OAAA,mBAAAiC,oBAAA,CAAAjC,OAAA,IAAAkC,IAAA,UAAAuC,SAAA;QAAA,IAAA7C,GAAA;QAAA,WAAAK,oBAAA,CAAAjC,OAAA,IAAAoC,IAAA,UAAAsC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApC,IAAA,GAAAoC,SAAA,CAAAnC,IAAA;YAAA;cACAgC,MAAA,CAAAtB,MAAA,CAAAC,MAAA;cACA;cAAA,KACAqB,MAAA,CAAAtE,WAAA,CAAAmB,QAAA;gBAAAsD,SAAA,CAAAnC,IAAA;gBAAA;cAAA;cAAAmC,SAAA,CAAAnC,IAAA;cAAA,OACAC,iBAAA,CAAAC,WAAA;gBAAAC,QAAA,EAAA6B,MAAA,CAAAtE,WAAA,CAAA0C;cAAA,GAAAC,IAAA,CACA,UAAAC,GAAA;gBACA,IAAA0B,MAAA,CAAAtE,WAAA,CAAAmB,QAAA;kBACAmD,MAAA,CAAAI,WAAA,CAAA9B,GAAA;kBACA0B,MAAA,CAAA1D,KAAA,GAAA+D,UAAA;oBACAL,MAAA,CAAAM,QAAA;sBAAAC,OAAA;sBAAA3E,IAAA;oBAAA;oBACAoE,MAAA,CAAAQ,aAAA;kBACA;gBACA,WAAAR,MAAA,CAAAtE,WAAA,CAAAmB,QAAA;kBACA,IAAA4D,CAAA,GAAA1B,QAAA,CAAA2B,aAAA;kBACAD,CAAA,CAAAE,IAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAxC,GAAA;kBACAmC,CAAA,CAAAM,QAAA;kBACAN,CAAA,CAAAO,KAAA;kBACAhB,MAAA,CAAA1D,KAAA,GAAA+D,UAAA;oBACAL,MAAA,CAAAM,QAAA;sBAAAC,OAAA;sBAAA3E,IAAA;oBAAA;oBACAoE,MAAA,CAAAQ,aAAA;kBACA;gBACA;cACA,CACA;YAAA;cAAAL,SAAA,CAAAnC,IAAA;cAAA;YAAA;cAEA;cACAZ,GAAA,GAAA2B,QAAA,CAAAkC,aAAA;cAEA,IAAA7D,GAAA;gBACA4C,MAAA,CAAAtB,MAAA,CAAAC,MAAA;gBACAqB,MAAA,CAAAkB,MAAA,CAAA9D,GAAA;gBACAiD,UAAA;kBACAL,MAAA,CAAAtB,MAAA,CAAAC,MAAA;gBACA;cACA;gBACAqB,MAAA,CAAA1D,KAAA,GAAA6E,WAAA;kBACA/D,GAAA,GAAA2B,QAAA,CAAAkC,aAAA;kBACA,IAAA7D,GAAA;oBACA4C,MAAA,CAAAkB,MAAA,CAAA9D,GAAA;oBACA4C,MAAA,CAAAQ,aAAA;oBACAR,MAAA,CAAAM,QAAA;sBAAAC,OAAA;sBAAA3E,IAAA;oBAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAuE,SAAA,CAAAvB,IAAA;UAAA;QAAA,GAAAqB,QAAA;MAAA;IAEA;IACAmB,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA;IACA;IACA,YACArE,WAAA,WAAAA,YAAA;MAAA,IAAAsE,MAAA;MAAA,WAAA9D,kBAAA,CAAAhC,OAAA,mBAAAiC,oBAAA,CAAAjC,OAAA,IAAAkC,IAAA,UAAA6D,SAAA;QAAA,IAAAjD,GAAA;QAAA,WAAAb,oBAAA,CAAAjC,OAAA,IAAAoC,IAAA,UAAA4D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,IAAA,GAAA0D,SAAA,CAAAzD,IAAA;YAAA;cAAA,KAGAsD,MAAA,CAAA5F,WAAA,CAAAgG,WAAA;gBAAAD,SAAA,CAAAzD,IAAA;gBAAA;cAAA;cACAsD,MAAA,CAAAnF,OAAA,GAAAwF,IAAA,CAAAC,KAAA,CAAAN,MAAA,CAAA5F,WAAA,CAAAgG,WAAA;cACAJ,MAAA,CAAApF,QAAA,CAAA2F,KAAA,GAAAP,MAAA,CAAAnF,OAAA,CAAA0F,KAAA;cACAP,MAAA,CAAAvF,SAAA,GAAAuF,MAAA,CAAAnF,OAAA,CAAA2F,OAAA;cACAR,MAAA,CAAAS,WAAA,GAAAT,MAAA,CAAAnF,OAAA,CAAA4F,WAAA;cACA1B,UAAA;gBACA;kBACAiB,MAAA,CAAApE,gBAAA;gBACA,SAAA8E,KAAA;cACA;cAAA,OAAAP,SAAA,CAAAQ,MAAA;YAAA;cAAAR,SAAA,CAAAzD,IAAA;cAAA,OAGAC,iBAAA,CAAAiE,YAAA,CAAAZ,MAAA,CAAA5F,WAAA,CAAA0C,UAAA;YAAA;cAAAE,GAAA,GAAAmD,SAAA,CAAAU,IAAA;cACA,IAAA7D,GAAA,CAAA8D,IAAA;gBACAd,MAAA,CAAApF,QAAA,GAAAoC,GAAA,CAAAxC,IAAA;gBACAwF,MAAA,CAAAnF,OAAA,GAAAwF,IAAA,CAAAC,KAAA,CAAAtD,GAAA,CAAAxC,IAAA,CAAAK,OAAA;gBACA,IAAAmF,MAAA,CAAAnF,OAAA;kBACAmF,MAAA,CAAAvF,SAAA,GAAAuF,MAAA,CAAAnF,OAAA,CAAA2F,OAAA;kBACAR,MAAA,CAAAS,WAAA,GAAAT,MAAA,CAAAnF,OAAA,CAAA4F,WAAA;gBACA;cACA;gBACAT,MAAA,CAAAhB,QAAA;kBAAAC,OAAA;kBAAA3E,IAAA;gBAAA;cACA;cACA;gBACA0F,MAAA,CAAApE,gBAAA;cACA,SAAA8E,KAAA;YAAA;YAAA;cAAA,OAAAP,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA;IACA;IACA,YACAc,iBAAA,WAAAA,kBAAAC,IAAA;MACA,KAAAA,IAAA,CAAAC,QAAA;MACA,IAAAD,IAAA,CAAAE,UAAA;QACA5B,MAAA,CAAA6B,IAAA,CAAAH,IAAA,CAAAC,QAAA;MACA,WAAAD,IAAA,CAAAE,UAAA;QACA5B,MAAA,CAAA6B,IAAA,CAAAH,IAAA,CAAAI,WAAA;MACA;IACA;IACA,YACAvF,eAAA,WAAAA,gBAAA;MAAA,IAAAwF,MAAA;MACA,IAAAC,KAAA;QACAC,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,UAAA;QACAC,IAAA;QACAC,OAAA;MACA;MACA,UAAAxH,WAAA,CAAAgG,WAAA,SAAAvF,OAAA,CAAAgH,QAAA;QACA,aAAAhH,OAAA,CAAAgH,QAAA;UACA;YACAP,KAAA,CAAAQ,MAAA;YACAP,KAAA,CAAAO,MAAA;YACAR,KAAA,CAAAS,IAAA;YACAR,KAAA,CAAAQ,IAAA;YACA;YACA,SAAAC,EAAA,MAAAA,EAAA,OAAAA,EAAA;cACAR,KAAA,CAAAS,IAAA,IAAAC,MAAA,CAAAF,EAAA,CAAAG,QAAA,GAAAL,MAAA,cAAAE,EAAA,GAAAA,EAAA;YACA;YACA;YACAzH,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAAwH,YAAA,EAAAC,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACAjB,KAAA,CAAAC,KAAA,CAAAiB,WAAA,CAAAF,GAAA,KAAAlB,MAAA,CAAAxG,OAAA,CAAAwH,YAAA,CAAArB,IAAA;YACA;YACAzG,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAA6H,YAAA,EAAAJ,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACAlB,KAAA,CAAAE,KAAA,CAAAiB,WAAA,CAAAF,GAAA,KAAAlB,MAAA,CAAAxG,OAAA,CAAAwH,YAAA,CAAArB,IAAA;YACA;YACA;UACA;YACAS,QAAA,GAAAkB,MAAA,MAAA9H,OAAA,CAAA+H,OAAA,CAAAJ,KAAA,UACAd,UAAA,GAAAiB,MAAA,MAAA9H,OAAA,CAAAgI,SAAA,CAAAL,KAAA,UACAb,IAAA,QAAA9G,OAAA,CAAAgI,SAAA,CAAAL,KAAA;YACAZ,OAAA,QAAA/G,OAAA,CAAA+H,OAAA,CAAAJ,KAAA;YACA;YACA,IAAAM,GAAA,GAAAH,MAAA,MAAA9H,OAAA,CAAA+H,OAAA,CAAAJ,KAAA;cACAO,KAAA,GAAAJ,MAAA,MAAA9H,OAAA,CAAAgI,SAAA,CAAAL,KAAA;cACAQ,GAAA,QAAAL,MAAA,CAAAjB,UAAA;YACA,IAAAoB,GAAA,GAAAC,KAAA;cACAtB,QAAA,GAAAA,QAAA,GAAAuB,GAAA;YACA;YACA1B,KAAA,CAAAQ,MAAA,GAAAL,QAAA;YACAF,KAAA,CAAAO,MAAA,GAAAL,QAAA;YACA;YACAH,KAAA,CAAAS,IAAA;YACAR,KAAA,CAAAQ,IAAA;YACA;YACA,IAAA5C,CAAA;cACA6C,CAAA;YACA,OAAAR,KAAA,CAAAM,MAAA,GAAAL,QAAA;cACAtC,CAAA,GAAA6C,CAAA;cACA,IAAAhB,IAAA;cACA,IAAAU,UAAA;gBACAM,CAAA,GAAAN,UAAA;cACA,WAAAA,UAAA;gBACAM,CAAA;gBACA7C,CAAA;cACA;cACA,IAAAuC,UAAA;gBACAV,IAAA,GAAAY,OAAA,UAAAI,CAAA,CAAAG,QAAA,GAAAL,MAAA,cAAAE,CAAA,GAAAA,CAAA;cACA;gBACAhB,IAAA,GAAAW,IAAA,UAAAK,CAAA,CAAAG,QAAA,GAAAL,MAAA,cAAAE,CAAA,GAAAA,CAAA;cACA;cACAR,KAAA,CAAAS,IAAA,CAAAjB,IAAA;cACAgB,CAAA,GAAA7C,CAAA;cACA,EAAAuC,UAAA;cACA,EAAAM,CAAA;YACA;YACA;YACAzH,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAAwH,YAAA,EAAAC,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACAjB,KAAA,CAAAC,KAAA,CAAAiB,WAAA,CAAAd,IAAA,SAAAY,GAAA,KACAlB,MAAA,CAAAxG,OAAA,CAAAwH,YAAA,CAAArB,IAAA;YACA;YACAzG,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAA6H,YAAA,EAAAJ,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACAlB,KAAA,CAAAE,KAAA,CAAAiB,WAAA,CAAAd,IAAA,SAAAY,GAAA,KACAlB,MAAA,CAAAxG,OAAA,CAAAwH,YAAA,CAAArB,IAAA;YACA;YACA;UACA;YACAS,QAAA,GAAAkB,MAAA,MAAA9H,OAAA,CAAA+H,OAAA,CAAAJ,KAAA;YACAd,UAAA,GAAAiB,MAAA,MAAA9H,OAAA,CAAAgI,SAAA,CAAAL,KAAA;YACAb,IAAA,QAAA9G,OAAA,CAAAgI,SAAA,CAAAL,KAAA;YACAlB,KAAA,CAAAQ,MAAA,GAAAL,QAAA;YACAF,KAAA,CAAAO,MAAA,GAAAL,QAAA;YACAH,KAAA,CAAAS,IAAA;YACAR,KAAA,CAAAQ,IAAA;YACA,SAAAC,GAAA,GAAAN,UAAA,EAAAM,GAAA,IAAAP,QAAA,EAAAO,GAAA;cACA,IAAAhB,KAAA,GAAAW,IAAA,UAAAK,GAAA,CAAAG,QAAA,GAAAL,MAAA,cAAAE,GAAA,GAAAA,GAAA;cACAR,KAAA,CAAAS,IAAA,CAAAjB,KAAA;YACA;;YAEA;YACAzG,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAAwH,YAAA,EAAAC,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACAjB,KAAA,CAAAC,KAAA,CAAAiB,WAAA,CAAAd,IAAA,SAAAY,GAAA,KACAlB,MAAA,CAAAxG,OAAA,CAAAwH,YAAA,CAAArB,IAAA;YACA;YAEAzG,MAAA,CAAA6H,IAAA,MAAAvH,OAAA,CAAA6H,YAAA,EAAAJ,OAAA,WAAAtB,IAAA;cACA,IAAAuB,GAAA,GAAAvB,IAAA,CAAAwB,KAAA;cACA,IAAAS,KAAA,GAAAzB,KAAA,CAAAiB,WAAA,CAAAd,IAAA,SAAAY,GAAA;cACAjB,KAAA,CAAA2B,KAAA,IAAA5B,MAAA,CAAAxG,OAAA,CAAA6H,YAAA,CAAA1B,IAAA;YACA;YACA;UACA;YACA;QACA;QACA,KAAAjG,KAAA;UACAT,IAAA;UACAE,IAAA,EAAAgH;QACA;MACA;QACA;;QAEA,IAAA0B,EAAA,QAAArI,OAAA,CAAA6H,YAAA;UACAS,GAAA,QAAAtI,OAAA,CAAAwH,YAAA;QAEA9H,MAAA,CAAA6H,IAAA,CAAAc,EAAA,EAAAZ,OAAA,WAAAtB,IAAA;UACAM,KAAA,CAAAW,IAAA,CAAAiB,EAAA,CAAAlC,IAAA;UACAQ,KAAA,CAAAS,IAAA,CAAAjB,IAAA;QACA;QACAzG,MAAA,CAAA6H,IAAA,CAAAe,GAAA,EAAAb,OAAA,WAAAtB,IAAA;UACAO,KAAA,CAAAU,IAAA,CAAAkB,GAAA,CAAAnC,IAAA;QACA;QACA,KAAAjG,KAAA;UACAT,IAAA;UACAE,IAAA,EAAAgH;QACA;MACA;MACA,QACA;QACA4B,IAAA;QACA5I,IAAA,EAAA8G,KAAA;QACAhH,IAAA;MACA,GACA;QACA8I,IAAA;QACA5I,IAAA,EAAA+G,KAAA;QACAjH,IAAA;MACA,EACA;IACA;IACA,WACAwE,WAAA,WAAAA,YAAA9B,GAAA;MACA,IAAAqG,QAAA,QAAAjJ,WAAA,CAAAkJ,UAAA;MACA,kBAAA7F,QAAA,CAAA2B,aAAA;QACA,IAAAD,CAAA,GAAA1B,QAAA,CAAA2B,aAAA;QACAD,CAAA,CAAAM,QAAA,GAAA4D,QAAA;QACAlE,CAAA,CAAAoE,KAAA,CAAAC,OAAA;QACArE,CAAA,CAAAE,IAAA,GAAAE,GAAA,CAAAC,eAAA,CAAAxC,GAAA;QACAS,QAAA,CAAAgG,IAAA,CAAAC,WAAA,CAAAvE,CAAA;QACAA,CAAA,CAAAO,KAAA;QACAH,GAAA,CAAAoE,eAAA,CAAAxE,CAAA,CAAAE,IAAA;QACA5B,QAAA,CAAAgG,IAAA,CAAAG,WAAA,CAAAzE,CAAA;MACA;QACA;QACA0E,SAAA,CAAAC,UAAA,CAAA9G,GAAA,EAAAqG,QAAA;MACA;IACA;IACAnE,aAAA,WAAAA,cAAA;MACA,KAAAa,KAAA;IACA;IACA,YACAvE,eAAA,WAAAA,gBAAA;MAAA,IAAAuI,MAAA;MAAA,WAAA7H,kBAAA,CAAAhC,OAAA,mBAAAiC,oBAAA,CAAAjC,OAAA,IAAAkC,IAAA,UAAA4H,SAAA;QAAA,IAAAhH,GAAA;QAAA,WAAAb,oBAAA,CAAAjC,OAAA,IAAAoC,IAAA,UAAA2H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzH,IAAA,GAAAyH,SAAA,CAAAxH,IAAA;YAAA;cACAqH,MAAA,CAAAI,kBAAA;cAAAD,SAAA,CAAAxH,IAAA;cAAA,OACAC,iBAAA,CAAAyH,UAAA;gBACAC,QAAA;gBACAC,OAAA,EAAAP,MAAA,CAAAQ,QAAA;gBACA1H,QAAA,EAAAkH,MAAA,CAAA3J,WAAA,CAAA0C;cACA;YAAA;cAJAE,GAAA,GAAAkH,SAAA,CAAArD,IAAA;cAKA,IAAA7D,GAAA,CAAA8D,IAAA;gBACAiD,MAAA,CAAA9I,UAAA,GAAA+B,GAAA,CAAAwH,IAAA;cACA;YAAA;YAAA;cAAA,OAAAN,SAAA,CAAA5G,IAAA;UAAA;QAAA,GAAA0G,QAAA;MAAA;IACA;IACA,YACAS,WAAA,WAAAA,YAAAzD,IAAA;MACA,IAAAA,IAAA,CAAAE,UAAA;QACA,IAAAF,IAAA,CAAAC,QAAA;UACA3B,MAAA,CAAA6B,IAAA,CAAAH,IAAA,CAAAC,QAAA;UACA;QACA;QACA,KAAAjC,QAAA;UAAAC,OAAA;QAAA;MACA,WAAA+B,IAAA,CAAAE,UAAA;QACA,IAAAF,IAAA,CAAAI,WAAA;UACA9B,MAAA,CAAA6B,IAAA,CAAAH,IAAA,CAAAI,WAAA;UACA;QACA;QACA,KAAApC,QAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAyF,aAAA,WAAAA,cAAA;IACAC,aAAA,MAAA3J,KAAA;EACA;AACA", "ignoreList": []}]}