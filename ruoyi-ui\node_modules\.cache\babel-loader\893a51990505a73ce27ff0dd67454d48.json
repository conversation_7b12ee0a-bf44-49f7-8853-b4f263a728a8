{"remainingRequest": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\project\\szs-dpx\\ruoyi-ui\\src\\api\\infoEscalation\\ai.js", "dependencies": [{"path": "F:\\project\\szs-dpx\\ruoyi-ui\\src\\api\\infoEscalation\\ai.js", "mtime": 1753692905473}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\babel.config.js", "mtime": 1745890588273}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "F:\\project\\szs-dpx\\ruoyi-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_auth", "require", "deepseekUrl", "deepseekApiKey", "deepseekModel", "ollamaUrl", "ollamaModel", "getBaseUrl", "protocol", "window", "location", "hostname", "port", "concat", "difyUrl", "deepseekAiQa", "_x", "_x2", "_deepseekAiQa", "apply", "arguments", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "text", "isStream", "url", "params", "req", "wrap", "_callee$", "_context", "prev", "next", "URL", "Object", "keys", "for<PERSON>ach", "key", "searchParams", "append", "Request", "method", "headers", "Authorization", "body", "JSON", "stringify", "model", "stream", "messages", "role", "content", "abrupt", "fetch", "stop", "difyAiQa", "_x3", "_x4", "_x5", "_difyAiQa", "_callee2", "responseMode", "config<PERSON><PERSON>", "_callee2$", "_context2", "console", "log", "getToken", "question", "response_mode", "ollamaAiQa", "_x6", "_x7", "_ollamaAiQa", "_callee3", "_callee3$", "_context3", "prompt", "options", "temperature", "num_ctx"], "sources": ["F:/project/szs-dpx/ruoyi-ui/src/api/infoEscalation/ai.js"], "sourcesContent": ["import { getToken } from \"@/utils/auth\";\r\n// deepseek\r\nconst deepseekUrl = `https://api.deepseek.com/v1/chat/completions`;\r\nconst deepseekApiKey = `***********************************`;\r\nconst deepseekModel = `deepseek-reasoner`;\r\n\r\n// 公司Ollama\r\nconst ollamaUrl = `http://***********:21434/api/generate`;\r\nconst ollamaModel = `deepseek-r1:32b`;\r\n\r\n// 公司Dify\r\n// const difyUrl = `http://************:9801`; // 本地\r\nconst getBaseUrl = () => {\r\n  const protocol = window.location.protocol; // 获取协议 (包含 \":\")\r\n  const hostname = window.location.hostname; // 获取主机名（IP或域名）\r\n  const port = window.location.port; // 获取端口号\r\n\r\n  // 如果端口号存在则添加，否则不添加\r\n  return `${protocol}//${hostname}${port ? \":\" + port : \"\"}`;\r\n};\r\nconst difyUrl = `${getBaseUrl()}/prod-api`; // 线上\r\n// const difyUrl = `https://***********:18006/prod-api`; // 准生产线上\r\n\r\nexport async function deepseekAiQa(text, isStream) {\r\n  const url = new URL(deepseekUrl);\r\n  const params = {};\r\n  Object.keys(params).forEach((key) => {\r\n    url.searchParams.append(key, params[key]);\r\n  });\r\n\r\n  const req = new Request(url, {\r\n    method: \"post\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${deepseekApiKey}`,\r\n    },\r\n    body: JSON.stringify({\r\n      model: deepseekModel,\r\n      stream: isStream,\r\n      messages: [\r\n        {\r\n          role: \"user\",\r\n          content: text,\r\n        },\r\n      ],\r\n    }),\r\n  });\r\n  return fetch(req);\r\n}\r\n\r\nexport async function difyAiQa(text, responseMode, configKey) {\r\n  console.log(difyUrl);\r\n  let url = new URL(`${difyUrl}/dify/chat/stream`);\r\n  const params = {};\r\n  Object.keys(params).forEach((key) => {\r\n    url.searchParams.append(key, params[key]);\r\n  });\r\n\r\n  const req = new Request(url, {\r\n    method: \"post\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${getToken()}`,\r\n    },\r\n    body: JSON.stringify({\r\n      question: text,\r\n      configKey: configKey,\r\n      response_mode: responseMode,\r\n    }),\r\n  });\r\n  return fetch(req);\r\n}\r\n\r\nexport async function ollamaAiQa(text, isStream) {\r\n  const url = new URL(ollamaUrl);\r\n  const params = {};\r\n  Object.keys(params).forEach((key) => {\r\n    url.searchParams.append(key, params[key]);\r\n  });\r\n\r\n  const req = new Request(url, {\r\n    method: \"post\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: ollamaModel,\r\n      prompt: text,\r\n      stream: isStream,\r\n      options: {\r\n        temperature: 1,\r\n        num_ctx: 10240,\r\n      },\r\n    }),\r\n  });\r\n  return fetch(req);\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA;AACA,IAAMC,WAAW,iDAAiD;AAClE,IAAMC,cAAc,wCAAwC;AAC5D,IAAMC,aAAa,sBAAsB;;AAEzC;AACA,IAAMC,SAAS,0CAA0C;AACzD,IAAMC,WAAW,oBAAoB;;AAErC;AACA;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EACvB,IAAMC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ,CAAC,CAAC;EAC3C,IAAMG,QAAQ,GAAGF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAAC,CAAC;EAC3C,IAAMC,IAAI,GAAGH,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAC,CAAC;;EAEnC;EACA,UAAAC,MAAA,CAAUL,QAAQ,QAAAK,MAAA,CAAKF,QAAQ,EAAAE,MAAA,CAAGD,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,EAAE;AAC1D,CAAC;AACD,IAAME,OAAO,MAAAD,MAAA,CAAMN,UAAU,CAAC,CAAC,cAAW,CAAC,CAAC;AAC5C;AAAA,SAEsBQ,YAAYA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,cAAA;EAAAA,aAAA,OAAAG,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,CAA3B,SAAAC,QAA4BC,IAAI,EAAEC,QAAQ;IAAA,IAAAC,GAAA,EAAAC,MAAA,EAAAC,GAAA;IAAA,WAAAP,oBAAA,CAAAD,OAAA,IAAAS,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;QAAA;UACzCP,GAAG,GAAG,IAAIQ,GAAG,CAAClC,WAAW,CAAC;UAC1B2B,MAAM,GAAG,CAAC,CAAC;UACjBQ,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,OAAO,CAAC,UAACC,GAAG,EAAK;YACnCZ,GAAG,CAACa,YAAY,CAACC,MAAM,CAACF,GAAG,EAAEX,MAAM,CAACW,GAAG,CAAC,CAAC;UAC3C,CAAC,CAAC;UAEIV,GAAG,GAAG,IAAIa,OAAO,CAACf,GAAG,EAAE;YAC3BgB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,YAAAjC,MAAA,CAAYV,cAAc;YACzC,CAAC;YACD4C,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBC,KAAK,EAAE9C,aAAa;cACpB+C,MAAM,EAAExB,QAAQ;cAChByB,QAAQ,EAAE,CACR;gBACEC,IAAI,EAAE,MAAM;gBACZC,OAAO,EAAE5B;cACX,CAAC;YAEL,CAAC;UACH,CAAC,CAAC;UAAA,OAAAO,QAAA,CAAAsB,MAAA,WACKC,KAAK,CAAC1B,GAAG,CAAC;QAAA;QAAA;UAAA,OAAAG,QAAA,CAAAwB,IAAA;MAAA;IAAA,GAAAhC,OAAA;EAAA,CAClB;EAAA,OAAAP,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEqBsC,QAAQA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,SAAA,CAAA3C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA0C,UAAA;EAAAA,SAAA,OAAAzC,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,CAAvB,SAAAuC,SAAwBrC,IAAI,EAAEsC,YAAY,EAAEC,SAAS;IAAA,IAAArC,GAAA,EAAAC,MAAA,EAAAC,GAAA;IAAA,WAAAP,oBAAA,CAAAD,OAAA,IAAAS,IAAA,UAAAmC,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAjC,IAAA,GAAAiC,SAAA,CAAAhC,IAAA;QAAA;UAC1DiC,OAAO,CAACC,GAAG,iNAACvD,OAAO,CAAC;UAChBc,GAAG,GAAG,IAAIQ,GAAG,IAAAvB,MAAA,CAAIC,OAAO,sBAAmB,CAAC;UAC1Ce,MAAM,GAAG,CAAC,CAAC;UACjBQ,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,OAAO,CAAC,UAACC,GAAG,EAAK;YACnCZ,GAAG,CAACa,YAAY,CAACC,MAAM,CAACF,GAAG,EAAEX,MAAM,CAACW,GAAG,CAAC,CAAC;UAC3C,CAAC,CAAC;UAEIV,GAAG,GAAG,IAAIa,OAAO,CAACf,GAAG,EAAE;YAC3BgB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClCC,aAAa,YAAAjC,MAAA,CAAY,IAAAyD,cAAQ,EAAC,CAAC;YACrC,CAAC;YACDvB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBsB,QAAQ,EAAE7C,IAAI;cACduC,SAAS,EAAEA,SAAS;cACpBO,aAAa,EAAER;YACjB,CAAC;UACH,CAAC,CAAC;UAAA,OAAAG,SAAA,CAAAZ,MAAA,WACKC,KAAK,CAAC1B,GAAG,CAAC;QAAA;QAAA;UAAA,OAAAqC,SAAA,CAAAV,IAAA;MAAA;IAAA,GAAAM,QAAA;EAAA,CAClB;EAAA,OAAAD,SAAA,CAAA3C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEqBqD,UAAUA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,WAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwD,YAAA;EAAAA,WAAA,OAAAvD,kBAAA,CAAAC,OAAA,mBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,CAAzB,SAAAqD,SAA0BnD,IAAI,EAAEC,QAAQ;IAAA,IAAAC,GAAA,EAAAC,MAAA,EAAAC,GAAA;IAAA,WAAAP,oBAAA,CAAAD,OAAA,IAAAS,IAAA,UAAA+C,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;QAAA;UACvCP,GAAG,GAAG,IAAIQ,GAAG,CAAC/B,SAAS,CAAC;UACxBwB,MAAM,GAAG,CAAC,CAAC;UACjBQ,MAAM,CAACC,IAAI,CAACT,MAAM,CAAC,CAACU,OAAO,CAAC,UAACC,GAAG,EAAK;YACnCZ,GAAG,CAACa,YAAY,CAACC,MAAM,CAACF,GAAG,EAAEX,MAAM,CAACW,GAAG,CAAC,CAAC;UAC3C,CAAC,CAAC;UAEIV,GAAG,GAAG,IAAIa,OAAO,CAACf,GAAG,EAAE;YAC3BgB,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDE,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBC,KAAK,EAAE5C,WAAW;cAClB0E,MAAM,EAAEtD,IAAI;cACZyB,MAAM,EAAExB,QAAQ;cAChBsD,OAAO,EAAE;gBACPC,WAAW,EAAE,CAAC;gBACdC,OAAO,EAAE;cACX;YACF,CAAC;UACH,CAAC,CAAC;UAAA,OAAAJ,SAAA,CAAAxB,MAAA,WACKC,KAAK,CAAC1B,GAAG,CAAC;QAAA;QAAA;UAAA,OAAAiD,SAAA,CAAAtB,IAAA;MAAA;IAAA,GAAAoB,QAAA;EAAA,CAClB;EAAA,OAAAD,WAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}]}